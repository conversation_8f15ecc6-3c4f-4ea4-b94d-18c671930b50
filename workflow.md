# 视频字幕处理工作流程

## 总体流程

视频字幕处理工具的工作流程如下：

1. **视频转写** → **字幕优化** → **字幕翻译**

每个步骤都可以单独执行，也可以按照完整流程一次性处理。

## 详细工作流程

### 模型类型说明

本系统使用两套不同的模型系统：

#### Whisper转录模型
- **用途**：视频音频转录为文字字幕
- **存储位置**：配置文件中的`model_dir`目录
- **命名规则**：文件夹名去掉"faster-"前缀，再去掉"whisper-"部分（如"faster-whisper-large-v2" → "large-v2"，"faster-distil-whisper-large-v3.5" → "distil-large-v3.5"）
- **常见模型**：large-v2, large-v3, medium, small等

#### Ollama翻译模型
- **用途**：字幕文本翻译
- **服务方式**：通过本地Ollama服务提供
- **获取方式**：使用`ollama list`查看，`ollama pull 模型名`安装
- **常见模型**：qwen系列、phi系列、mistral系列等

### 1. 视频转写流程

#### 整体处理模式 (whole)

```
输入视频 → Whisper模型整体处理 → 生成原文字幕
```

- 使用配置中的`whole_model`指定的模型
- 一次性处理整个视频文件
- 适合较短的视频文件

#### 分段处理模式 (segment)

```
输入视频 → 分段 → 首段使用first_segment_model处理 → 其他段使用other_segments_model处理 → 合并结果 → 生成原文字幕
```

- 将视频按照`segment_length`设置的长度（秒）分段
- 首段使用`first_segment_model`指定的模型（通常更精确但较慢）
- 其他段使用`other_segments_model`指定的模型（通常更快）
- 适合较长的视频文件，可以平衡处理效率和质量

### 2. 字幕优化流程

```
原文字幕 → 过滤无意义词 → 合并短句 → 格式化 → 生成优化后字幕
```

- 根据`min_sentence_length`设置过滤过短的句子
- 根据`merge_interval`设置合并时间间隔较近的字幕条目
- 如果`remove_meaningless`设置为True，则移除配置的无意义词
- 优化后的字幕保存为新文件

### 3. 字幕翻译流程

```
优化后字幕 → 分批 → Ollama翻译 → 合并结果 → 生成双语字幕
```

- 将字幕按照`batch_size`设置的条数分批处理
- 使用`model`指定的Ollama模型进行翻译
- 使用`max_workers`设置的线程数并行处理
- 翻译结果与原文合并生成双语字幕

### 4. 双语字幕拆分流程

```
双语字幕 → 提取原文 → 提取翻译 → 分别保存为独立文件
```

- 自动检测字幕文件是否为双语格式
- 将原文和翻译文本分别提取
- 保持原始时间戳信息
- 生成两个独立的字幕文件

## 文件处理模式

1. **完整处理模式**
   - 依次执行：视频转写 → 字幕优化 → 字幕翻译
   - 适用于从视频到双语字幕的一站式处理

2. **仅转写模式**
   - 只执行视频转写步骤
   - 输出原始字幕文件
   - 适用于只需要原文字幕的情况

3. **仅优化模式**
   - 只执行字幕优化步骤
   - 适用于已有原始字幕但需要优化的情况

4. **重新翻译模式**
   - 如果是双语字幕，先拆分再重新翻译
   - 如果是单语字幕，先优化再翻译
   - 适用于对翻译质量不满意需要重新处理的情况

## 配置更新流程

所有的配置通过设置界面更新：

```
打开设置对话框 → 修改参数 → 保存设置 → 更新config.yaml文件
```

配置的更改将在下次操作时生效。

## 批量处理流程

当处理多个文件时，工具会按顺序处理每个文件：

```
文件1处理完成 → 文件2处理 → ... → 文件n处理
```

处理过程中可随时停止。已处理的文件结果会保留。 