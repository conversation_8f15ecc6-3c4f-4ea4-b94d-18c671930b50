{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pysrt\n", "import re\n", "from translator import SubtitleTranslator\n", "from call_ollama import generate_with_ollama\n", "ST=SubtitleTranslator()\n", "import config\n", "cfg=config.load_config()['translation']\n", "\n", "model_name=cfg['model']\n", "system_prompt=cfg['system_prompt']\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from utill import is_video_subtitle,is_subtitle_video,merge_bilingual_subtitles\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["vpath=r\"H:\\download\\New Sensations - <PERSON> - <PERSON> - Cheaters Can't Resist BBC - <PERSON> Slips Off Her Dress And Ring For BBC - 02.01.2023.mp4\""]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["WindowsPath(\"H:/download/New Sensations - <PERSON> - <PERSON> - Cheaters Can't Resist BBC - <PERSON> Slips Off Her Dress And <PERSON> For BBC - 02.01.2023.srt\")"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["merge_bilingual_subtitles(vpath)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from call_ollama import generate_with_ollama\n", "import pysrt"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def chunk_list_overlap(lst, n, overlap=0):\n", "    step = n - overlap\n", "    if overlap >= n:\n", "        raise ValueError(\"Overlap can't be greater than or equal to chunk size.\")\n", "    return [lst[i:i + n] for i in range(0, len(lst), step)]\n", "\n", "\n", "def srt2str(lst):\n", "    return '\\n'.join([str(n) for n in lst])\n", "\n", "\n", "\n", "\n", "def merged_srt(en_subs, zh_subs):\n", "    \"\"\"\n", "    合并中英文字幕为双语字幕\n", "    :param en_subs: 英文字幕对象组 (pysrt.SubRipFile 或 list)\n", "    :param zh_subs: 中文字幕对象组 (pysrt.SubRipFile 或 list)\n", "    :return: 合并后的双语字幕对象组 (pysrt.SubRipFile)\n", "    \"\"\"\n", "    # 处理输入参数，确保是列表格式\n", "    if hasattr(en_subs, '__iter__') and not isinstance(en_subs, str):\n", "        en_list = list(en_subs)\n", "    else:\n", "        raise ValueError(\"英文字幕参数必须是可迭代的字幕对象\")\n", "    \n", "    if hasattr(zh_subs, '__iter__') and not isinstance(zh_subs, str):\n", "        zh_list = list(zh_subs)\n", "    else:\n", "        raise ValueError(\"中文字幕参数必须是可迭代的字幕对象\")\n", "    \n", "    # 检查长度是否一致\n", "    if len(en_list) != len(zh_list):\n", "        print(f\"警告：中英文字幕长度不一致 (英文: {len(en_list)}, 中文: {len(zh_list)})\")\n", "        # 取较短的长度，避免索引越界\n", "        min_length = min(len(en_list), len(zh_list))\n", "        en_list = en_list[:min_length]\n", "        zh_list = zh_list[:min_length]\n", "        print(f\"已截取到相同长度: {min_length}\")\n", "    \n", "    # 创建新的字幕列表\n", "    merged_subs = []\n", "    \n", "    for i, (en_sub, zh_sub) in enumerate(zip(en_list, zh_list)):\n", "        try:\n", "            # 确保时间信息存在且有效\n", "            start_time = en_sub.start if hasattr(en_sub, 'start') and en_sub.start else zh_sub.start\n", "            end_time = en_sub.end if hasattr(en_sub, 'end') and en_sub.end else zh_sub.end\n", "            \n", "            # 获取文本内容，处理空文本的情况\n", "            en_text = en_sub.text.strip() if hasattr(en_sub, 'text') and en_sub.text else \"\"\n", "            zh_text = zh_sub.text.strip() if hasattr(zh_sub, 'text') and zh_sub.text else \"\"\n", "            \n", "            # 跳过双语字幕都为空的情况\n", "            if not en_text and not zh_text:\n", "                continue\n", "            \n", "            # 构建双语文本\n", "            if en_text and zh_text:\n", "                merged_text = f\"{en_text}\\n{zh_text}\"\n", "            elif en_text:\n", "                merged_text = en_text\n", "            elif zh_text:\n", "                merged_text = zh_text\n", "            else:\n", "                continue  # 理论上不会到达这里，但保险起见\n", "            \n", "            # 创建新的字幕项\n", "            new_sub = pysrt.SubRipItem(\n", "                index=len(merged_subs) + 1,  # 重新编号，确保连续\n", "                start=start_time,\n", "                end=end_time,\n", "                text=merged_text\n", "            )\n", "            merged_subs.append(new_sub)\n", "            \n", "        except Exception as e:\n", "            print(f\"处理第 {i+1} 条字幕时出错: {str(e)}\")\n", "            continue\n", "    \n", "    # 转换为 pysrt.SubRipFile 对象\n", "    try:\n", "        merged_file = pysrt.SubRipFile(items=merged_subs)\n", "        print(f\"成功合并 {len(merged_subs)} 条双语字幕\")\n", "        return merged_file\n", "    except Exception as e:\n", "        print(f\"创建字幕文件对象时出错: {str(e)}\")\n", "        return None\n", "\n", "def save_merged_subtitle(merged_subs, output_path):\n", "    \"\"\"\n", "    保存合并后的字幕到文件\n", "    :param merged_subs: 合并后的字幕对象组\n", "    :param output_path: 输出文件路径\n", "    :return: 是否保存成功\n", "    \"\"\"\n", "    try:\n", "        if merged_subs is None:\n", "            print(\"字幕对象为空，无法保存\")\n", "            return False\n", "        \n", "        merged_subs.save(output_path, encoding='utf-8')\n", "        print(f\"双语字幕已保存到: {output_path}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"保存字幕文件时出错: {str(e)}\")\n", "        return False\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["sys_prompt=r\"你是一个字幕翻译助手。能把字幕中的英语对话准确的翻译为中文对话。在能充分理解原对话中前后文的情况下，能用合适的符合中文表达习惯的语言准确的翻译出来，只翻译文字部分，不翻译人名，序号和时间码保持原样不翻译。\""]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["input_srt=r\"H:\\download\\She's <PERSON> - <PERSON> - <PERSON> - A <PERSON><PERSON> Newcomer - Meet <PERSON>! - 31.03.2025.srt\""]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["srt_obj=pysrt.open(input_srt)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["437"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["len(translate_srt_seg)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功合并 10 条双语字幕\n"]}], "source": ["merged_file=merged_srt(en_srt_seg,zh_srt_seg)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["SubRipFile([<pysrt.srtitem.SubRipItem at 0x1bc63238710>,\n", "            <pysrt.srtitem.SubRipItem at 0x1bc632398e0>,\n", "            <pysrt.srtitem.SubRipItem at 0x1bc63238560>,\n", "            <pysrt.srtitem.SubRipItem at 0x1bc63238d10>,\n", "            <pysrt.srtitem.SubRipItem at 0x1bc632399a0>,\n", "            <pysrt.srtitem.SubRipItem at 0x1bc632383b0>,\n", "            <pysrt.srtitem.SubRipItem at 0x1bc632399d0>,\n", "            <pysrt.srtitem.SubRipItem at 0x1bc63239580>,\n", "            <pysrt.srtitem.SubRipItem at 0x1bc63239fa0>,\n", "            <pysrt.srtitem.SubRipItem at 0x1bc63239be0>])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_file"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "00:00:14,010 --> 00:00:15,760\n", "Well I think she's here.\n", "我觉得她在这儿。\n", "\n", "2\n", "00:00:19,120 --> 00:00:20,120\n", "Come on in <PERSON>!\n", "快进来，黛西！\n", "\n", "3\n", "00:00:24,220 --> 00:00:25,220\n", "Hello there.\n", "你好。\n", "\n", "4\n", "00:00:26,545 --> 00:00:28,710\n", "and you're a Daisy fox yeah?\n", "你是只黛西狐獴吧？\n", "\n", "5\n", "00:00:30,050 --> 00:00:32,050\n", "Will we go ahead take look at your IDs real quick\n", "我们先看看你的身份证吧。\n", "\n", "6\n", "00:00:32,051 --> 00:00:34,331\n", "Alright everything seems to be an order 19 years old huh.\n", "好的，看起来都没问题。19岁？\n", "\n", "7\n", "00:00:36,870 --> 00:00:38,590\n", "All right everything seems to be in order.\n", "好的，看来都符合条件。\n", "\n", "8\n", "00:00:39,250 --> 00:00:40,450\n", "19 years old huh?\n", "19岁？\n", "\n", "9\n", "00:00:41,590 --> 00:00:43,090\n", "There you go! You take that back.\n", "你刚才说的，我拿回来给你！\n", "\n", "10\n", "00:00:44,330 --> 00:00:46,680\n", "excited To Be here Very Excited.\n", "能来这儿真是太激动了。非常激动。\n", "\n"]}], "source": ["print('\\n'.join([str(l) for l in merged_file]))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "00:00:14,010 --> 00:00:15,760\n", "我觉得她在这儿。\n", "\n", "2\n", "00:00:19,120 --> 00:00:20,120\n", "快进来，黛西！\n", "\n", "3\n", "00:00:24,220 --> 00:00:25,220\n", "你好。\n", "\n", "4\n", "00:00:26,545 --> 00:00:28,710\n", "你是只黛西狐獴吧？\n", "\n", "5\n", "00:00:30,050 --> 00:00:32,050\n", "我们先看看你的身份证吧。\n", "\n", "6\n", "00:00:32,051 --> 00:00:34,331\n", "好的，看起来都没问题。19岁？\n", "\n", "7\n", "00:00:36,870 --> 00:00:38,590\n", "好的，看来都符合条件。\n", "\n", "8\n", "00:00:39,250 --> 00:00:40,450\n", "19岁？\n", "\n", "9\n", "00:00:41,590 --> 00:00:43,090\n", "你刚才说的，我拿回来给你！\n", "\n", "10\n", "00:00:44,330 --> 00:00:46,680\n", "能来这儿真是太激动了。非常激动。\n"]}, {"ename": "AttributeError", "evalue": "module 'pysrt' has no attribute 'subtitle'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[11]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      3\u001b[39m translate_srt_seg=generate_with_ollama(system_prompt=sys_prompt,user_input=srt_seg)\n\u001b[32m      4\u001b[39m zh_srt_seg=pysrt.from_string(translate_srt_seg)\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m dub_srt=\u001b[43mmerged_srt\u001b[49m\u001b[43m(\u001b[49m\u001b[43men_srt_seg\u001b[49m\u001b[43m,\u001b[49m\u001b[43mzh_srt_seg\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      6\u001b[39m \u001b[38;5;28mprint\u001b[39m(dub_srt)\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mbreak\u001b[39;00m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 31\u001b[39m, in \u001b[36mmerged_srt\u001b[39m\u001b[34m(en_subs, zh_subs)\u001b[39m\n\u001b[32m     28\u001b[39m     merged_subs.append(new_sub)\n\u001b[32m     30\u001b[39m \u001b[38;5;66;03m# 转换为 pysrt.subtitle.SubRipFile 类型（可选）\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m31\u001b[39m merged_file = \u001b[43mpysrt\u001b[49m\u001b[43m.\u001b[49m\u001b[43msubtitle\u001b[49m.SubRipFile(items=merged_subs)\n\u001b[32m     33\u001b[39m \u001b[38;5;66;03m# 保存为新文件\u001b[39;00m\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m merged_file\n", "\u001b[31mAttributeError\u001b[39m: module 'pysrt' has no attribute 'subtitle'"]}], "source": ["for en_srt_seg in chunk_list_overlap(srt_obj,10,3):\n", "    srt_seg=srt2str(en_srt_seg)\n", "    translate_srt_seg=generate_with_ollama(system_prompt=sys_prompt,user_input=srt_seg)\n", "    zh_srt_seg=pysrt.from_string(translate_srt_seg)\n", "    dub_srt=merged_srt(en_srt_seg,zh_srt_seg)\n", "    print(dub_srt)\n", "    break\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["SubRipFile([<pysrt.srtitem.SubRipItem at 0x2cd5fb8e450>,\n", "            <pysrt.srtitem.SubRipItem at 0x2cd5fb8d760>,\n", "            <pysrt.srtitem.SubRipItem at 0x2cd5fb8e8d0>,\n", "            <pysrt.srtitem.SubRipItem at 0x2cd5fb8e630>,\n", "            <pysrt.srtitem.SubRipItem at 0x2cd5fb8de50>,\n", "            <pysrt.srtitem.SubRipItem at 0x2cd5fb8ee10>,\n", "            <pysrt.srtitem.SubRipItem at 0x2cd5fb8ec60>,\n", "            <pysrt.srtitem.SubRipItem at 0x2cd5fb8e240>,\n", "            <pysrt.srtitem.SubRipItem at 0x2cd5fb8f800>])"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["pysrt.from_string(translate_srt_seg)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "00:00:14,010 --> 00:00:15,760\n", " Well，我想她在这儿。\n", "\n", "2\n", "00:00:19,120 --> 00:00:20,120\n", " 进来吧，黛西！\n", "\n", "3\n", "00:00:24,220 --> 00:00:25,220\n", " 你好。\n", "\n", "4\n", "00:00:26,545 --> 00:00:28,710\n", " 而且你是黛西狐对吧？\n", "\n", "5\n", "00:00:30,050 --> 00:00:32,050\n", " 我们先看看你们的证件行吗\n", "\n", "6\n", "00:00:32,051 --> 00:00:34,331\n", " 好吧，看来一切都很正常。十九岁是吧。\n", "\n", "7\n", "00:00:36,870 --> 00:00:38,590\n", " 好的，看来一切都很正常。\n", "\n", "8\n", "00:00:39,250 --> 00:00:40,450\n", " 十九岁是吧？\n", "\n", "9\n", "00:00:41,590 --> 00:00:43,090\n", " 哦，给你！你收回那个说法。\n"]}, {"data": {"text/plain": ["'1\\n00:00:14,010 --> 00:00:15,760\\n Well，我想她在这儿。\\n\\n2\\n00:00:19,120 --> 00:00:20,120\\n 进来吧，黛西！\\n\\n3\\n00:00:24,220 --> 00:00:25,220\\n 你好。\\n\\n4\\n00:00:26,545 --> 00:00:28,710\\n 而且你是黛西狐对吧？\\n\\n5\\n00:00:30,050 --> 00:00:32,050\\n 我们先看看你们的证件行吗\\n\\n6\\n00:00:32,051 --> 00:00:34,331\\n 好吧，看来一切都很正常。十九岁是吧。\\n\\n7\\n00:00:36,870 --> 00:00:38,590\\n 好的，看来一切都很正常。\\n\\n8\\n00:00:39,250 --> 00:00:40,450\\n 十九岁是吧？\\n\\n9\\n00:00:41,590 --> 00:00:43,090\\n 哦，给你！你收回那个说法。'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["generate_with_ollama(system_prompt=sys_prompt,user_input=usr_prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}