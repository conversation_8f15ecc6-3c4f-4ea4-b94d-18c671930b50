# 字幕处理系统项目框架
## 项目简介
本项目旨在自动化处理视频字幕，包括生成字幕、优化字幕、翻译字幕和打包视频文件。通过结合语音识别、字幕优化、翻译和文件打包技术，实现字幕处理的自动化流程。
## 项目结构
```
srt/
├── main.py                # 主程序入口
├── optimize_subtitle.py   # 字幕优化模块
├── translate_srt.py       # 字幕翻译模块
├── subtitle_pack.py       # 文件打包模块
├── generate_subtitles.py  # 字幕生成核心模块
├── config.yaml             # 配置文件处理
├── Pointless_word.txt     # 无效词过滤列表
```

## 各模块功能

### 1. 程序入口
```python
def main():
    srt_processor = SubtitleProcessor()  # 初始化处理器
    while True:
        path = input("请输入文件路径...")  # 获取用户输入
        if 是文件: process_single_video()
        elif 是目录: process_directory()
```
### 2.单文件处理模块
```python
def process_single_file():
    if 是视频文件 and 没有同名字幕文件: 
        generate_subtitle_by_xxl()
    elif 是字幕文件 and 没有同名的视频文件:
        pass
    elif 是字幕文件 and 有同名的视频文件:
        optimize_srt()
        if 字幕文件是双语字幕 or 视频文件是中文字幕:
            pass
        else:
            translate_srt()
        if 自动打包:
            make_video_folder()
    else:
        print("无法处理该文件")
```

### 3. 目录处理模块
```python
def process_directory():
    for file in 目录下所有文件:
        if 是视频文件 or 是字幕文件:
            process_single_video()
```

### 4. 字幕生成模块
```python
def generate_subtitle_by_xxl(video_path):
    使用whisper-fast模型进行语音识别
    输出格式：视频同名.srt文件
    参数配置：config.yaml
```

### 5. 字幕优化模块
```python
def optimize_srt(input_path):
    1. 加载Pointless_word.txt中的无效词列表
    2. 合并相邻相同内容字幕，并重置时间戳（扩展为总合并时间）
    3. 删除包含无效词的字幕项
    4. 保存优化后的字幕文件
```

### 6. 字幕翻译模块
```python
def translate_srt(input_path):
    1. 读取字幕文件
    2. 调用translate_subtitle()进行翻译
    3. 调用clean_translation()进行清理
    4. 保存翻译后的双语字幕文件
```

### 7. 文件打包模块
```python
def make_video_folder():
    1. 创建临时文件夹
    2. 将视频、字幕、翻译后的双语字幕文件复制到临时文件夹
    3. 打包成zip文件
```