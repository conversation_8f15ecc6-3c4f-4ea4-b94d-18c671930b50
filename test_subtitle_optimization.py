#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕质量优化功能测试脚本
"""

import os
import tempfile
from pathlib import Path
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from generate_subtitles import (
    parse_srt_file, 
    find_long_segments, 
    calculate_duration,
    optimize_subtitle_quality
)
import config

def create_test_srt():
    """创建测试用的SRT文件"""
    test_content = """1
00:00:00,000 --> 00:00:05,000
这是一个正常长度的字幕。

2
00:00:05,000 --> 00:00:20,000
这是一个超过10秒的长字幕，需要被优化。这个字幕包含了很多内容，可能是因为音频识别不准确导致的。

3
00:00:20,000 --> 00:00:25,000
这又是一个正常长度的字幕。

4
00:00:25,000 --> 00:00:45,000
这是另一个超过10秒的长字幕，也需要被优化。这种长时间的字幕通常表示音频识别存在问题，需要重新转写来提高质量。

5
00:00:45,000 --> 00:00:50,000
最后一个正常长度的字幕。
"""
    
    # 创建临时SRT文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8')
    temp_file.write(test_content)
    temp_file.close()
    
    return temp_file.name

def test_parse_srt():
    """测试SRT文件解析功能"""
    print("=== 测试SRT文件解析功能 ===")
    
    srt_path = create_test_srt()
    try:
        subtitles = parse_srt_file(srt_path)
        
        print(f"解析到 {len(subtitles)} 个字幕条目:")
        for sub in subtitles:
            print(f"  {sub['index']}: {sub['start_time']} --> {sub['end_time']} (时长: {sub['duration']:.1f}秒)")
            print(f"    内容: {sub['text'][:50]}...")
        
        return subtitles
    finally:
        os.unlink(srt_path)

def test_find_long_segments():
    """测试长时间片段检测功能"""
    print("\n=== 测试长时间片段检测功能 ===")
    
    srt_path = create_test_srt()
    try:
        subtitles = parse_srt_file(srt_path)
        long_segments = find_long_segments(subtitles, threshold=10.0)
        
        print(f"找到 {len(long_segments)} 个超过10秒的长时间片段:")
        for seg in long_segments:
            print(f"  片段 {seg['index']}: {seg['start_time']} --> {seg['end_time']} (时长: {seg['duration']:.1f}秒)")
            print(f"    内容: {seg['text'][:50]}...")
        
        return long_segments
    finally:
        os.unlink(srt_path)

def test_calculate_duration():
    """测试时长计算功能"""
    print("\n=== 测试时长计算功能 ===")
    
    test_cases = [
        ("00:00:00,000", "00:00:05,000", 5.0),
        ("00:00:05,000", "00:00:20,000", 15.0),
        ("00:01:00,500", "00:01:30,750", 30.25),
    ]
    
    for start, end, expected in test_cases:
        duration = calculate_duration(start, end)
        print(f"  {start} --> {end}: {duration:.3f}秒 (期望: {expected:.3f}秒)")
        assert abs(duration - expected) < 0.001, f"时长计算错误: {duration} != {expected}"
    
    print("  ✓ 时长计算功能正常")

def test_config_loading():
    """测试配置加载功能"""
    print("\n=== 测试配置加载功能 ===")
    
    cfg = config.load_config()
    quality_config = cfg.get('subtitle_quality', {})
    
    print("字幕质量优化配置:")
    print(f"  启用优化: {quality_config.get('enable_optimization', True)}")
    print(f"  时长阈值: {quality_config.get('duration_threshold', 10.0)}秒")
    print(f"  优化模型: {quality_config.get('optimization_model', 'large-v3-turbo-ct2')}")
    print(f"  最大优化片段数: {quality_config.get('max_segments_to_optimize', 5)}")

def main():
    """主测试函数"""
    print("字幕质量优化功能测试")
    print("=" * 50)
    
    try:
        # 测试基础功能
        test_calculate_duration()
        test_parse_srt()
        test_find_long_segments()
        test_config_loading()
        
        print("\n" + "=" * 50)
        print("✓ 所有基础功能测试通过！")
        print("\n注意: 完整的优化功能需要:")
        print("1. 有效的视频文件")
        print("2. 配置好的Whisper模型")
        print("3. FFmpeg工具")
        print("\n要测试完整功能，请使用实际的视频文件运行字幕生成程序。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
