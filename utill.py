import os
import config
from pathlib import Path
import re
import pysrt
from typing import List, Tuple
import shutil

def is_video_subtitle(path: Path) -> bool:
    if type(path) != Path:
        path = Path(path)
    """判断视频文件同目录下是否存在字幕文件"""
    for ext in config.load_config()['filetype']['subtitle_extensions']:
        sub_path = path.with_suffix(ext)
        if os.path.exists(sub_path):
            return True
    return False

def is_subtitle_video(path: Path) -> bool:
    if type(path) != Path:
        path = Path(path)
    """判断字幕文件同目录下是否存在视频文件"""
    for ext in config.load_config()['video']['extensions']:
        video_path = path.with_suffix(ext)
        if os.path.exists(video_path):
            return True
    return False


def is_chinese(text: str) -> bool:
    """
    检查文本是否为中文
    通过检查是否包含中文字符且不含日语假名来判断
    """
    # 检查是否包含汉字
    has_hanzi = any('\u4e00' <= char <= '\u9fff' for char in text)
    
    # 检查是否包含日语假名
    has_kana = any('\u3040' <= char <= '\u309F' or  # 平假名
                   '\u30A0' <= char <= '\u30FF'      # 片假名
                   for char in text)
    
    # 如果包含汉字但不包含假名，则可能是中文
    return has_hanzi and not has_kana

def is_english(text: str) -> bool:
    """
    检查文本是否包含英文字符
    """
    return bool(re.search('[a-zA-Z]', text))


def parse_srt(content: str) -> List[Tuple[str, str, str]]:
    """
    使用 pysrt 解析 SRT 文件内容
    返回个列表，每项包含：序号、时间戳、字幕文本
    """
    try:
        # pysrt 需要从文件读取，所以我们先创建一个临时文件
        temp_file = 'temp_srt.srt'
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        subs = pysrt.open(temp_file)
        result = []
        for sub in subs:
            # pysrt 解析后的格式：
            # sub.index: 字幕序号
            # sub.start, sub.end: 开始和结束时间
            # sub.text: 字幕内容
            timestamp = f"{sub.start} --> {sub.end}"
            result.append((str(sub.index), timestamp, sub.text))
        
        # 删除临时文件
        os.remove(temp_file)
        return result
    except Exception as e:
        print(f"解析字幕文件时出错: {e}")
        if os.path.exists('temp_srt.srt'):
            os.remove('temp_srt.srt')
        return []


def is_bilingual(subtitle):
    """
    判断字幕是否为双语字幕。
    假设如果大部分字幕条目包含两行或以上，且两行文本不同，则为双语字幕。

    参数:
        subtitle (pysrt.SubRipFile): 要检查的字幕文件。

    返回:
        bool: 如果是双语字幕返回True，否则返回False。
    """
    bilingual_count = 0
    total_entries = len(subtitle)
    
    if total_entries == 0:
        return False

    for sub in subtitle:
        lines = sub.text.strip().split('\n')
        if len(lines) >= 2 and lines[0].strip() != lines[1].strip():
            bilingual_count += 1
    
    # 如果超过50%的字幕条目符合双语特征，则认为是双语字幕
    return (bilingual_count / total_entries) > 0.5

def is_bilingual_subtitle(srt_path: str) -> bool:
    """
    检查字幕文件是否已经是双语字幕
    通过采样前50条字幕来判断
    参数：
        srt_path: 字幕文件路径
    返回：
        bool: 是否为双语字幕
    """
    
    try:
        # 读取字幕文件
        with open(str(srt_path), 'r', encoding='utf-8') as f:
            content = f.read()
            
        subtitle_parts = parse_srt(content)
        # 只检查前50条字幕
        sample_size = min(50, len(subtitle_parts))
        bilingual_count = 0
        
        for i in range(sample_size):
            text = subtitle_parts[i][2].strip()
            # 将文本按行分割，检查是否存在中英文行
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            if len(lines) >= 2:  # 至少有两行
                has_chinese = any(is_chinese(line) for line in lines)
                has_english = any(is_english(line) for line in lines)
                if has_chinese and has_english:
                    bilingual_count += 1
        
        # 如果超过60%的样本都是双语的，认为这是双语字幕
        return (bilingual_count / sample_size) > 0.6
    except Exception as e:
        print(f"检查双语字幕时出错: {e}")
        return False


def is_chinese_text(text: str, strict: bool = False) -> bool:
    """
    判断文本是否为中文文本
    
    参数:
        text (str): 需要判断的文本
        strict (bool): 判断模式
            - True: 严格模式，要求文本必须全部是中文字符
            - False: 宽松模式，只要不是纯英文就算中文
            默认为 False（宽松模式）
    
    返回:
        bool: 是否为中文文本
    """
    if strict:
        # 严格模式：检查是否全是中文字符
        chinese_pattern = r'^[\u4e00-\u9fff\u3000-\u303f\uFF00-\uFFEF\s\,\.\!\?\:\;\@\#\$\%\^\&\*\(\)\-\_\+\=\[\]\{\}\"\'\|\\\/\<\>\~\`\·\？\！\，\。\；\：\「\」\『\』\《\》\【\】\（\）\——\～\…\、]+$'
        return bool(re.match(chinese_pattern, text))
    else:
        # 宽松模式：只要不是纯英文就算中文
        english_pattern = r'^[a-zA-Z0-9\s\,\.\!\?\:\;\@\#\$\%\^\&\*\(\)\-\_\+\=\[\]\{\}\"\'\|\\\/\<\>\~\`]+$'
        return not bool(re.match(english_pattern, text))

def is_chinese_subtitle(srt_path: str,strict:bool=True) -> bool:
    """
    检查字幕文件是否为中文
    """
    chines_count = 0
    subs=pysrt.open(srt_path)
    for sub in subs[:50]:
        if is_chinese_text(sub.text,strict):
            chines_count += 1
        else:
            pass
    if chines_count>10:
        return True
    else:
        return False
            
def is_pointless(text, pointless_words):
    """
    判断文本是否为无意义内容，忽略标点和大小写
    :param text: 输入文本
    :param pointless_words: 无意义词和短语列表
    :return: 是否为无意义内容
    """
    # 移除文本中的标点符号并转换为小写
    cleaned_text = remove_punctuation(text.lower())
    
    # 将清理后的文本分割成单词
    words = cleaned_text.split()
    
    # 检查是否所有单词都在 pointless_words 列表中
    if all(word in pointless_words for word in words):
        return True
    
    # 检查整个清理后的文本是否匹配任何无意义词或短语
    for word in pointless_words:
        if cleaned_text == word:
            return True
    
    return False

def read_pointless_words():
    """
    读取无意义词列表
    优先从配置中的meaningless_words读取，如果没有则从文件读取
    :return: 无意义词和短语列表
    """
    cfg = config.load_config()
    
    # 优先使用新的配置方式（从GUI设置中）
    meaningless_words = cfg.get('optimize', {}).get('meaningless_words', [])
    if meaningless_words:
        print(f"从配置中读取到 {len(meaningless_words)} 个无意义词")
        return [remove_punctuation(word.lower()) for word in meaningless_words if word.strip()]
    
    # 回退到旧的文件方式
    file_path = cfg.get('optimization', {}).get('pointless_words_file', 'Pointless_word.txt')
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"无意义词文件不存在: {file_path}，将从 Pointless_word.txt 读取")
        file_path = 'Pointless_word.txt'
    
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                words = [remove_punctuation(line.strip().lower()) for line in f if line.strip()]
                print(f"从文件 {file_path} 读取到 {len(words)} 个无意义词")
                return words
        except Exception as e:
            print(f"读取无意义词文件时出错: {e}")
    
    # 如果都失败，返回默认列表
    print("无法读取无意义词，使用默认列表")
    default_words = ['yes', 'yeah', 'oh', 'um', 'uh', 'mm', 'mmm', 'ah', 'ahh']
    return [remove_punctuation(word.lower()) for word in default_words]
    
def remove_punctuation(text):
    """
    移除文本中的标点符号
    :param text: 输入文本
    :return: 移除标点符号后的文本
    """
    return re.sub(r'[^\w\s]', '', text)

def split_sub(srt_path):
    """
    拆分单个双语字幕文件，将中文字幕和原语言字幕分开。
    通过语言识别确定哪一行是中文，哪一行是原语言。
    
    参数:
        srt_path (str): 双语字幕文件的路径。

    返回:
        tuple: (中文字幕路径, 原文字幕路径)
    """
    if not is_bilingual_subtitle(srt_path):
        print(f"{srt_path} 不是双语字幕，跳过拆分。")
        return None, None
    
    subs = pysrt.open(srt_path)
    # 创建两个新的字幕对象
    zh_subs = pysrt.SubRipFile()
    original_subs = pysrt.SubRipFile()
    
    # 记录分析情况
    total_subtitles = len(subs)
    detected_chinese = 0
    problematic_subtitles = 0
    
    for sub in subs:
        # 分割每个字幕条目的文本
        lines = sub.text.split('\n')
        
        if len(lines) >= 2:
            # 使用语言检测确定哪行是中文
            first_is_chinese = is_chinese_text(lines[0])
            second_is_chinese = is_chinese_text(lines[1])
            
            # 如果有一行是中文，一行不是，则可以明确区分
            if first_is_chinese and not second_is_chinese:
                zh_line = lines[0]
                original_line = lines[1]
                detected_chinese += 1
            elif not first_is_chinese and second_is_chinese:
                zh_line = lines[1]
                original_line = lines[0]
                detected_chinese += 1
            # 如果两行都是中文或都不是中文，尝试进一步判断
            else:
                # 检查是否为英文
                first_is_english = is_english(lines[0])
                second_is_english = is_english(lines[1])
                
                if first_is_english and not second_is_english:
                    # 第一行是英文，第二行可能是中文或其他语言
                    zh_line = lines[1]
                    original_line = lines[0]
                elif not first_is_english and second_is_english:
                    # 第二行是英文，第一行可能是中文或其他语言
                    zh_line = lines[0]
                    original_line = lines[1]
                else:
                    # 难以判断，默认第一行为原文，第二行为中文
                    # 这里可以添加更复杂的语言检测逻辑
                    print(f"警告: 无法准确区分字幕语言: \n第一行: {lines[0]}\n第二行: {lines[1]}")
                    zh_line = lines[1]  # 默认第二行为中文
                    original_line = lines[0]  # 默认第一行为原文
                    problematic_subtitles += 1
            
            # 创建原语言字幕条目
            original_sub = pysrt.SubRipItem(
                index=sub.index,
                start=sub.start,
                end=sub.end,
                text=original_line
            )            
            original_subs.append(original_sub)
            
            # 创建中文字幕条目
            zh_sub = pysrt.SubRipItem(
                index=sub.index,
                start=sub.start,
                end=sub.end,
                text=zh_line
            )
            zh_subs.append(zh_sub)
        else:
            # 只有一行文本，判断是中文还是原文
            if is_chinese_text(lines[0]):
                # 是中文
                zh_sub = pysrt.SubRipItem(
                    index=sub.index,
                    start=sub.start,
                    end=sub.end,
                    text=lines[0]
                )
                zh_subs.append(zh_sub)
                
                # 原文留空
                original_sub = pysrt.SubRipItem(
                    index=sub.index,
                    start=sub.start,
                    end=sub.end,
                    text=""
                )
                original_subs.append(original_sub)
            else:
                # 不是中文，视为原文
                original_sub = pysrt.SubRipItem(
                    index=sub.index,
                    start=sub.start,
                    end=sub.end,
                    text=lines[0]
                )
                original_subs.append(original_sub)
                
                # 中文留空
                zh_sub = pysrt.SubRipItem(
                    index=sub.index,
                    start=sub.start,
                    end=sub.end,
                    text=""
                )
                zh_subs.append(zh_sub)
            
            problematic_subtitles += 1
    
    # 保存拆分后的字幕文件
    folder_path = os.path.dirname(srt_path)
    base_name, _ = os.path.splitext(os.path.basename(srt_path))
    
    # 保存文件
    zh_path = os.path.join(folder_path, f"{base_name}.zh.srt")
    zh_subs.save(zh_path, encoding='utf-8')
    
    original_path = os.path.join(folder_path, f"{base_name}.srt")
    original_subs.save(original_path, encoding='utf-8')
    
    # 输出分析结果
    print(f"{srt_path} 拆分完成:")
    print(f"总字幕条目: {total_subtitles}")
    print(f"成功识别中文的条目: {detected_chinese}")
    print(f"可能存在问题的条目: {problematic_subtitles}")
    print(f"中文字幕保存为: {zh_path}")
    print(f"原文字幕保存为: {original_path}")
    
    return original_path, zh_path


def merge_bilingual_subtitles(file_path):
    """
    合并字幕文件，将.srt和.zh.srt合并为双语字幕
    
    参数:
        file_path (str/Path): 字幕文件或视频文件路径
        
    返回:
        Path/None: 合并后的字幕文件路径，失败则返回None
    """
    if type(file_path) != Path:
        file_path = Path(file_path)
    
    # 确定字幕文件路径
    if is_video_subtitle(file_path):
        # 如果是视频文件，获取对应的字幕文件路径
        srt_path = file_path.with_suffix('.srt')
        zh_srt_path = file_path.with_suffix('.zh.srt')
    elif file_path.suffix == '.srt':
        # 如果是原语言字幕文件
        srt_path = file_path
        zh_srt_path = file_path.with_suffix('.zh.srt')
    elif file_path.suffix == '.zh.srt':
        # 如果是中文字幕文件
        zh_srt_path = file_path
        srt_path = file_path.with_name(file_path.stem.replace('.zh', '').replace('zh.', '') + '.srt')
    else:
        print(f"错误: 不支持的文件类型: {file_path}")
        return None

    # 检查文件是否存在
    if not os.path.exists(srt_path):
        print(f"错误: 找不到对应的原始字幕文件: {srt_path}")
        return None
    if not os.path.exists(zh_srt_path):
        print(f"错误: 找不到对应的翻译字幕文件: {zh_srt_path}")
        return None
    
    # 检查是否已经是双语字幕
    if is_bilingual_subtitle(srt_path):
        print(f"提示: {srt_path} 已经是双语字幕文件")
        return srt_path
        
    # 合并字幕
    print(f"开始合并字幕: {srt_path} 和 {zh_srt_path}")
    bilingual_sub_path = file_path.with_suffix('.bilingual.srt')
    merged_srt = pysrt.SubRipFile()
    
    try:
        original_srt = pysrt.open(srt_path)
        translated_srt = pysrt.open(zh_srt_path)
        
        if len(original_srt) != len(translated_srt):
            print(f"警告: 原始字幕和翻译字幕条目数不一致 ({len(original_srt)} vs {len(translated_srt)})")
            print(f"将使用较短的字幕条目数进行合并")
            
        # 使用较短的字幕长度
        min_length = min(len(original_srt), len(translated_srt))
        
        for i in range(min_length):
            index = original_srt[i].index
            timestart = original_srt[i].start
            timeend = original_srt[i].end
            original_text = original_srt[i].text
            translated_text = translated_srt[i].text
            
            # 确保文本不为空
            if not original_text.strip() and not translated_text.strip():
                continue  # 跳过两者都为空的情况
                
            # 合并文本，如果任一为空则不添加额外换行
            if not original_text.strip():
                merged_text = translated_text
            elif not translated_text.strip():
                merged_text = original_text
            else:
                merged_text = original_text + '\n' + translated_text
                
            merged_srt.append(pysrt.SubRipItem(
                index=index,
                start=timestart,
                end=timeend,
                text=merged_text
            ))
        
        # 保存合并后的字幕
        merged_srt.save(bilingual_sub_path, encoding='utf-8')
        
        # 检查是否成功保存
        if os.path.exists(bilingual_sub_path):
            # 备份原始文件
            backup_path = srt_path.with_suffix('.srt.bak')
            if os.path.exists(srt_path):
                shutil.copy2(srt_path, backup_path)
            
            # 替换原始字幕
            os.remove(srt_path)
            os.rename(bilingual_sub_path, srt_path)
            
            print(f"字幕合并完成: {srt_path}")
            print(f"原始字幕已备份为: {backup_path}")
            return srt_path
        else:
            print(f"错误: 保存合并字幕失败")
            return None
    except Exception as e:
        print(f"合并字幕时出错: {str(e)}")
        if os.path.exists(bilingual_sub_path):
            os.remove(bilingual_sub_path)
        return None


import time
from functools import wraps

def timer(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.perf_counter()
        result = func(*args, **kwargs)
        elapsed = time.perf_counter() - start
        print(f"{func.__name__} 耗时: {elapsed:.3f}秒")
        return result
    return wrapper

def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除可能导致问题的字符
    """
    # 保留扩展名
    name, ext = os.path.splitext(filename)
    
    # 移除特殊字符，只保留字母、数字、下划线
    clean_name = re.sub(r'[^\w\s-]', '', name)
    
    # 将空格替换为下划线
    clean_name = clean_name.replace(' ', '_')
    
    # 确保文件名不会太长
    if len(clean_name) > 50:
        clean_name = clean_name[:50]
    
    return clean_name + ext

def prepare_file_for_whisper(file_path: str) -> tuple[str, str]:
    """
    准备文件用于 Whisper 处理
    返回: (临时文件路径, 原始文件路径)
    """
    original_path = Path(file_path)
    temp_dir = original_path.parent / "temp_whisper"
    temp_dir.mkdir(exist_ok=True)
    
    # 创建安全的文件名
    safe_filename = sanitize_filename(original_path.name)
    temp_path = temp_dir / safe_filename
    
    # 复制文件到临时位置
    shutil.copy2(file_path, temp_path)
    
    return str(temp_path), str(original_path)

def cleanup_temp_whisper_file(temp_path: str):
    """
    清理临时文件
    """
    try:
        temp_file = Path(temp_path)
        if temp_file.exists():
            temp_file.unlink()
            
        # 如果临时目录为空，也删除它
        temp_dir = temp_file.parent
        if temp_dir.name == "temp_whisper" and not any(temp_dir.iterdir()):
            temp_dir.rmdir()
    except Exception as e:
        print(f"清理临时文件时出错: {e}")