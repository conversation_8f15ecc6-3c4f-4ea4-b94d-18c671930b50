import os
from datetime import timedelta
from utill import read_pointless_words,is_pointless
import pysrt
from collections import Counter
from pathlib import Path
import re
import traceback


def optimize_srt(input_file, progress_callback=None, stopped_flag=None):
    if type(input_file) == str:
        input_file=Path(input_file)
    """
    优化srt字幕文件
    :param input_file: 输入的srt文件路径
    :param progress_callback: 进度回调函数，用于更新UI进度
    :param stopped_flag: 用于检查是否应停止处理的回调函数
    """
    # 读取无意义词和短语列表
    pointless_phrases = set(read_pointless_words())
    
    # 读取SRT文件
    try:
        subs = pysrt.open(input_file, encoding='utf-8')
    except Exception as e:
        print(f"打开字幕文件失败: {str(e)}")
        return None
        
    total_subs = len(subs)
    print(f'优化前{total_subs} 条字幕')
    
    # 设置总进度
    if progress_callback:
        progress_callback.update(0, f"开始优化字幕文件: {input_file.name}")
    
    # 优化字幕
    optimized_subs = pysrt.SubRipFile()
    processed_count = 0
    deleted_duplicates = 0
    removed_duplicate_lines = 0
    merged_subtitles = 0  # 记录合并的字幕数量
    removed_pointless = 0  # 记录删除的无意义字幕数量
    multiline_converted = 0  # 记录转换为单行的字幕数量
    update_interval = max(1, total_subs // 100)  # 每完成1%更新一次UI
    
    try:
        for i, sub in enumerate(subs):
            # --- 检查停止标志 ---
            if stopped_flag and stopped_flag():
                print("字幕优化操作已停止")
                if progress_callback:
                    progress_callback.update(message="优化操作已停止") # Update status
                return None # Indicate stopped
            # ----------------------
            
            # 更新进度
            processed_count += 1
            
            # 更新处理进度 - 确保实时反馈到UI
            if progress_callback and (i % update_interval == 0 or processed_count == total_subs):
                progress_percentage = (processed_count / total_subs) * 100
                progress_callback.update(progress_percentage, f"优化字幕: {processed_count}/{total_subs}")
            
            # 检查是否为无意义内容
            if is_pointless(sub.text, pointless_phrases):
                removed_pointless += 1
                continue
            
            # 处理单条字幕内的重复行
            original_text = sub.text
            sub.text, lines_removed = remove_duplicate_lines(sub.text)
            removed_duplicate_lines += lines_removed
            
            # 将多行字幕转换为单行
            if '\n' in sub.text:
                sub.text = ' '.join(line.strip() for line in sub.text.split('\n') if line.strip())
                multiline_converted += 1
            
            # 检查是否存在大量重复内容
            optimized_text = drop_mass_duplicates(sub.text)
            
            # 如果优化后文本为空，则跳过该字幕
            if not optimized_text.strip():
                continue
                
            # 规则1：检测相邻字幕内容相同的情况
            # 如果当前字幕与前一个字幕内容相同，则延长前一个字幕的结束时间，并跳过当前字幕
            if optimized_subs and optimized_subs[-1].text == optimized_text:
                # 合并时间码（延长前一个字幕的结束时间）
                optimized_subs[-1].end = sub.end
                deleted_duplicates += 1
            # 规则2：检测相邻字幕时间间隔过小的情况
            # 如果当前字幕开始时间与前一个字幕结束时间相差小于0.1秒，则合并字幕
            elif optimized_subs and should_merge_subtitles(optimized_subs[-1], sub):
                # 合并字幕内容和时间码
                optimized_subs[-1].text = merge_subtitle_texts(optimized_subs[-1].text, optimized_text)
                optimized_subs[-1].end = sub.end
                merged_subtitles += 1
            else:
                # 创建新的字幕项
                new_sub = pysrt.SubRipItem(
                    index=len(optimized_subs) + 1,
                    start=sub.start,
                    end=sub.end,
                    text=optimized_text
                )
                optimized_subs.append(new_sub)
        
        # --- 检查停止标志 (结束后) ---
        if stopped_flag and stopped_flag():
            print("字幕优化操作已停止 (在保存前)")
            if progress_callback:
                progress_callback.update(message="优化操作已停止")
            return None
        # -----------------------------
        
        # 保存优化后的字幕
        output_file=input_file
        optimized_subs.save(output_file, encoding='utf-8')
        
        print(f'优化后{len(optimized_subs)} 条字幕，已删除 {deleted_duplicates} 条重复字幕，合并了 {merged_subtitles} 条相邻字幕，'
              f'移除了 {removed_duplicate_lines} 处字幕内重复行，删除了 {removed_pointless} 条无意义字幕，'
              f'将 {multiline_converted} 条多行字幕转换为单行')
        print(f"优化完成,输出文件: {output_file}")
        
        # 完成处理，更新进度到100%
        if progress_callback:
            progress_callback.update(100, f"字幕优化完成: {len(optimized_subs)}/{total_subs} 条字幕保留，"
                                      f"删除重复内容 {deleted_duplicates} 条，合并相邻字幕 {merged_subtitles} 条，"
                                      f"移除字幕内重复行 {removed_duplicate_lines} 处，删除无意义字幕 {removed_pointless} 条，"
                                      f"转换多行字幕 {multiline_converted} 条")
            
        return output_file
        
    except Exception as e:
        print(f"优化字幕时发生错误: {str(e)}")
        traceback.print_exc()
        if progress_callback:
             progress_callback.update(message=f"优化字幕失败: {str(e)}") # Update status on error
        return None

def should_merge_subtitles(prev_sub, current_sub, threshold_seconds=0.3):
    """
    判断是否应该合并两个相邻的字幕
    :param prev_sub: 前一个字幕项
    :param current_sub: 当前字幕项  
    :param threshold_seconds: 时间间隔阈值（秒）
    :return: 是否应该合并
    """
    # 将 SubRipTime 转换为总毫秒数进行计算
    def subriptime_to_seconds(srt_time):
        """将 SubRipTime 转换为秒数"""
        return srt_time.hours * 3600 + srt_time.minutes * 60 + srt_time.seconds + srt_time.milliseconds / 1000.0
    
    # 计算前一个字幕结束时间和当前字幕开始时间的差值
    prev_end_seconds = subriptime_to_seconds(prev_sub.end)
    current_start_seconds = subriptime_to_seconds(current_sub.start)
    time_gap = current_start_seconds - prev_end_seconds
    
    # 如果时间间隔小于阈值，则应该合并
    return time_gap <= threshold_seconds

def merge_subtitle_texts(text1, text2):
    """
    合并两个字幕的文本内容
    :param text1: 第一个字幕的文本
    :param text2: 第二个字幕的文本
    :return: 合并后的文本
    """
    # 去除首尾空白字符
    text1 = text1.strip()
    text2 = text2.strip()
    
    # 如果第一个文本为空，直接返回第二个文本
    if not text1:
        return text2
    
    # 如果第二个文本为空，直接返回第一个文本
    if not text2:
        return text1
    
    # 检查第一个文本是否以标点符号结尾
    punctuation = {'.', '!', '?', '。', '！', '？', ',', '，', ';', '；', ':', '：'}
    
    # 如果第一个文本不以标点符号结尾，添加空格连接
    if text1[-1] not in punctuation:
        return f"{text1} {text2}"
    else:
        # 如果以标点符号结尾，直接连接（可能需要换行）
        return f"{text1}\n{text2}"

def remove_duplicate_lines(text):
    """
    删除字幕中的重复行
    :param text: 字幕文本内容
    :return: 处理后的文本，以及移除的重复行数量
    """
    lines = text.split('\n')
    if len(lines) <= 1:
        return text, 0
    
    # 去除重复行
    unique_lines = []
    removed_count = 0
    
    for line in lines:
        line = line.strip()
        if line and line not in unique_lines:
            unique_lines.append(line)
        elif line:
            removed_count += 1
    
    # 如果有行被移除，返回处理后的文本
    if removed_count > 0:
        return '\n'.join(unique_lines), removed_count
    
    return text, 0

def drop_mass_duplicates(text, threshold=0.8):
    # 处理连续重复字符的情况，如 "AHHHHH" 或 "ahhhhhhhh"
    # 使用正则表达式匹配连续重复3个以上的相同字符
    text = re.sub(r'([A-Za-z])\1{2,}', r'\1', text)
    
    # 处理重复的单词或短语
    text_list = re.split(r'[\,]', text)
    counts = Counter(text_list)
    for w in counts.most_common():
        if w[1] > 4:
            text = text.replace(w[0], '')
    text = text.replace(',', '')
    return text

def parse_subtitles(lines):
    """解析SRT文件内容为字典列表"""
    subtitles = []
    current_sub = {}
    for line in lines:
        line = line.strip()
        if not line:
            if current_sub:
                subtitles.append(current_sub)
                current_sub = {}
        elif line.isdigit():
            current_sub["index"] = int(line)
        elif "-->" in line:
            start, end = line.split(" --> ")
            current_sub["start"] = parse_time(start.strip())
            current_sub["end"] = parse_time(end.strip())
        else:
            current_sub["text"] = current_sub.get("text", "") + line + " "
    return subtitles

def filter_pointless_words(subtitles, pointless_words):
    """过滤包含无效词的字幕项"""
    return [sub for sub in subtitles if 
            not any(word in sub["text"] for word in pointless_words)]

def parse_time(time_str):
    """将时间字符串转换为timedelta"""
    h, m, s = time_str.split(':')
    s, ms = s.split(',')
    return timedelta(
        hours=int(h),
        minutes=int(m),
        seconds=int(s),
        milliseconds=int(ms)
    )

def write_subtitles(output_path, subtitles):
    """将优化后的字幕写入文件"""
    with open(output_path, "w", encoding="utf-8") as f:
        for i, sub in enumerate(subtitles, 1):
            f.write(f"{i}\n")
            f.write(f"{format_time(sub['start'])} --> {format_time(sub['end'])}\n")
            f.write(f"{sub['text'].strip()}\n\n")

def format_time(td):
    """将timedelta格式化为SRT时间码"""
    total_seconds = int(td.total_seconds())
    ms = td.microseconds // 1000
    h, r = divmod(total_seconds, 3600)
    m, s = divmod(r, 60)
    return f"{h:02d}:{m:02d}:{s:02d},{ms:03d}"

def merged_srt(en_subs, zh_subs):
    """
    合并中英文字幕为双语字幕
    :param en_subs: 英文字幕对象组 (pysrt.SubRipFile 或 list)
    :param zh_subs: 中文字幕对象组 (pysrt.SubRipFile 或 list)
    :return: 合并后的双语字幕对象组 (pysrt.SubRipFile)
    """
    # 处理输入参数，确保是列表格式
    if hasattr(en_subs, '__iter__') and not isinstance(en_subs, str):
        en_list = list(en_subs)
    else:
        raise ValueError("英文字幕参数必须是可迭代的字幕对象")
    
    if hasattr(zh_subs, '__iter__') and not isinstance(zh_subs, str):
        zh_list = list(zh_subs)
    else:
        raise ValueError("中文字幕参数必须是可迭代的字幕对象")
    
    # 检查长度是否一致
    if len(en_list) != len(zh_list):
        print(f"警告：中英文字幕长度不一致 (英文: {len(en_list)}, 中文: {len(zh_list)})")
        # 取较短的长度，避免索引越界
        min_length = min(len(en_list), len(zh_list))
        en_list = en_list[:min_length]
        zh_list = zh_list[:min_length]
        print(f"已截取到相同长度: {min_length}")
    
    # 创建新的字幕列表
    merged_subs = []
    
    for i, (en_sub, zh_sub) in enumerate(zip(en_list, zh_list)):
        try:
            # 确保时间信息存在且有效
            start_time = en_sub.start if hasattr(en_sub, 'start') and en_sub.start else zh_sub.start
            end_time = en_sub.end if hasattr(en_sub, 'end') and en_sub.end else zh_sub.end
            
            # 获取文本内容，处理空文本的情况
            en_text = en_sub.text.strip() if hasattr(en_sub, 'text') and en_sub.text else ""
            zh_text = zh_sub.text.strip() if hasattr(zh_sub, 'text') and zh_sub.text else ""
            
            # 跳过双语字幕都为空的情况
            if not en_text and not zh_text:
                continue
            
            # 构建双语文本
            if en_text and zh_text:
                merged_text = f"{en_text}\n{zh_text}"
            elif en_text:
                merged_text = en_text
            elif zh_text:
                merged_text = zh_text
            else:
                continue  # 理论上不会到达这里，但保险起见
            
            # 创建新的字幕项
            new_sub = pysrt.SubRipItem(
                index=len(merged_subs) + 1,  # 重新编号，确保连续
                start=start_time,
                end=end_time,
                text=merged_text
            )
            merged_subs.append(new_sub)
            
        except Exception as e:
            print(f"处理第 {i+1} 条字幕时出错: {str(e)}")
            continue
    
    # 转换为 pysrt.SubRipFile 对象
    try:
        merged_file = pysrt.SubRipFile(items=merged_subs)
        print(f"成功合并 {len(merged_subs)} 条双语字幕")
        return merged_file
    except Exception as e:
        print(f"创建字幕文件对象时出错: {str(e)}")
        return None

def save_merged_subtitle(merged_subs, output_path):
    """
    保存合并后的字幕到文件
    :param merged_subs: 合并后的字幕对象组
    :param output_path: 输出文件路径
    :return: 是否保存成功
    """
    try:
        if merged_subs is None:
            print("字幕对象为空，无法保存")
            return False
        
        merged_subs.save(output_path, encoding='utf-8')
        print(f"双语字幕已保存到: {output_path}")
        return True
    except Exception as e:
        print(f"保存字幕文件时出错: {str(e)}")
        return False

if __name__ == "__main__":
    import sys
    
    # 获取输入文件路径
    def get_input_file():
        if len(sys.argv) >= 2:
            # 有命令行参数，使用第一个参数作为文件路径
            return sys.argv[1]
        else:
            # 没有命令行参数，提示用户输入
            return input('请输入需要优化的字幕文件路径: ').replace('"', '').strip()
    
    # 处理文件
    def process_subtitle_file(file_path):
        print(f"开始优化字幕文件: {file_path}")
        result_path = optimize_srt(file_path)
        if result_path:
            print(f"字幕优化完成: {result_path}")
        else:
            print("字幕优化失败")
        return result_path
    
    # 主执行流程
    try:
        input_file = get_input_file()
        if input_file:
            process_subtitle_file(input_file)
        else:
            print("未提供有效的文件路径")
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")