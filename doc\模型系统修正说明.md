# 模型系统修正说明

## 问题描述

之前的代码中混淆了Whisper模型和Ollama模型的用途，在设置对话框中错误地尝试从模型目录获取翻译模型列表。

## 正确的模型区分

### Whisper转录模型
- **用途**：视频音频转录为文字字幕（语音转文字）
- **存储位置**：配置文件中`paths.model_dir`指定的目录
- **获取方式**：扫描模型目录中的文件夹
- **命名规则**：去掉"faster-"前缀，再去掉"whisper-"前缀的文件夹名
- **示例**：
  - 文件夹：`faster-whisper-large-v2` → 模型名：`large-v2`
  - 文件夹：`faster-whisper-medium` → 模型名：`medium`
  - 文件夹：`faster-whisper-small` → 模型名：`small`

### Ollama翻译模型  
- **用途**：字幕文本翻译（语言转换）
- **服务方式**：通过本地Ollama服务提供
- **获取方式**：执行`ollama list`命令
- **安装方式**：`ollama pull 模型名`
- **示例**：qwen2.5:7b-instruct, phi3:latest, mistral:latest等

## 修改内容

### 1. gui.py修改

#### 新增方法：
- `get_whisper_models()` - 从模型目录获取Whisper转录模型
- `get_ollama_translation_models()` - 通过ollama list获取翻译模型

#### 修改方法：
- `setup_whisper_tab()` - 使用`get_whisper_models()`获取模型列表
- `setup_translation_tab()` - 使用`get_ollama_translation_models()`获取模型列表

#### 移除内容：
- 删除了错误的`get_ollama_models()`方法（该方法错误地从模型目录获取模型）

### 2. 文档更新

#### README.md
- 新增"模型配置说明"部分
- 明确区分Whisper转录模型和Ollama翻译模型

#### doc/README.md  
- 更新"自定义模型"部分
- 重写"注意事项"，强调模型区分

#### workflow.md
- 新增"模型类型说明"部分
- 详细说明两种模型的用途和获取方式

## 功能验证

修改后的功能：
1. ✅ Whisper选项卡正确显示模型目录中的转录模型
2. ✅ 翻译选项卡正确显示Ollama服务中的翻译模型  
3. ✅ 模型名称正确去掉"faster-"和"whisper-"前缀
4. ✅ 两套模型系统完全分离，不再混淆

## 配置示例

```yaml
paths:
  model_dir: "C:\\Users\\<USER>\\AppData\\Roaming\\Subtitle Edit\\Whisper\\Purfview-Whisper-Faster\\_models"

whisper:
  whole_model: "large-v2"        # Whisper转录模型
  first_segment_model: "large-v2" 
  other_segments_model: "large-v3-turbo"

translation:
  model: "qwen2.5:7b-instruct"          # Ollama翻译模型
```

## 使用说明

1. **配置Whisper模型**：确保模型下载到`model_dir`目录
2. **配置Ollama模型**：使用`ollama pull 模型名`安装翻译模型
3. **设置界面**：在Whisper选项卡配置转录，在翻译选项卡配置翻译
4. **处理流程**：视频 → Whisper转录 → 字幕优化 → Ollama翻译

## 测试验证

经过测试验证，模型名称提取逻辑完全正确：

### 模型名称转换示例
```
faster-whisper-large-v2             → large-v2
faster-whisper-medium               → medium  
faster-whisper-small                → small
faster-whisper-large-v3-turbo       → large-v3-turbo
faster-distil-whisper-large-v3.5    → distil-large-v3.5
whisper-base                        → base
large-v2                            → large-v2
```

### 实际测试结果
- ✅ 从模型目录成功获取3个Whisper转录模型
- ✅ 通过ollama list成功获取6个翻译模型
- ✅ 模型名称去前缀逻辑正确工作
- ✅ 两套模型系统完全分离

修正完成！现在Whisper转录模型和Ollama翻译模型在设置界面中完全正确分离。 