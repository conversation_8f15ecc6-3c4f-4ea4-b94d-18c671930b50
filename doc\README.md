# 视频字幕处理工具

一个全功能的视频字幕处理系统，支持视频转写、字幕优化、字幕翻译和双语字幕拆分等功能。

## 项目概述

本项目是一个基于Python的视频字幕处理工具，提供了友好的图形用户界面，让用户可以方便地进行视频转写、字幕优化和翻译等操作。通过整合多种开源模型和工具，本系统实现了高效且灵活的字幕处理流程。

## 主要功能

1. **视频转写**：将视频中的语音转换为字幕文件
   - 支持整段模式（适合小视频）和分段模式（适合大视频）
   - 可配置不同模型用于不同的视频片段，提高效率和质量

2. **字幕优化**：优化字幕文件内容和格式
   - 过滤无效词语
   - 合并短句
   - 调整时间戳
   - 改进排版

3. **字幕翻译**：使用本地大语言模型翻译字幕
   - 支持Ollama本地模型
   - 可定制翻译提示词
   - 批量处理提高翻译效率

4. **双语字幕拆分**：将双语字幕拆分为原文和中文字幕文件

5. **批量处理**：支持文件夹批量处理

## 系统要求

- 操作系统：Windows（主要支持）/ Linux / MacOS
- Python 3.8+
- GPU支持（推荐用于模型推理）
- 至少8GB RAM（推荐16GB以上）

## 安装步骤

1. 克隆本仓库：
   ```
   git clone https://github.com/yourusername/video-subtitle-processor.git
   cd video-subtitle-processor
   ```

2. 安装依赖：
   ```
   pip install -r requirements.txt
   ```

3. （可选）安装拖放支持：
   ```
   pip install tkinterdnd2
   ```

4. 安装Ollama（用于字幕翻译）：
   从[Ollama官网](https://ollama.ai/download)下载并安装

## 使用方法

1. 启动应用：
   ```
   python gui.py
   ```

2. 选择处理模式：
   - **仅视频转写**：只将视频转换为字幕
   - **仅字幕优化**：只优化现有字幕
   - **重新翻译**：重新翻译字幕
   - **全流程处理**：视频转写+优化+翻译的完整流程

3. 选择文件或文件夹：
   - 点击"浏览..."选择单个文件
   - 点击"选择文件夹"进行批量处理
   - 或直接拖放文件到应用窗口

4. 配置设置（可选）：
   - 点击"设置"按钮打开设置对话框
   - 调整各项参数

5. 开始处理：
   - 点击"开始处理"按钮
   - 在日志区域查看处理进度和结果

## 配置说明

系统配置存储在`config.yaml`文件中，包含以下主要部分：

### 文件类型配置
```yaml
filetype:
  video_extensions: [.mp4, .mkv, .avi, .mov, .webm]
  subtitle_extensions: [.srt, .ass, .vtt, .sub]
```

### 路径配置
```yaml
paths:
  whisper_path: whisperx_integration.py
  model_dir: models
  output_dir: ''  # 空表示输出到源文件所在目录
```

### 处理配置
```yaml
processing:
  max_workers: 4  # 并行处理线程数
  merge_threshold: 1.0  # 合并字幕的时间阈值（秒）
```

### Whisper配置
```yaml
whisper:
  process_mode: segment  # 处理模式: whole或segment
  whole_model: large-v3-turbo-ct2  # 整段模式使用的模型
  segment_length: 30  # 分段长度（分钟）
  first_segment_model: large-v3-turbo-ct2  # 第一段使用的模型
  other_segments_model: small  # 其他段使用的模型
  # 其他Whisper参数...
```

### 翻译配置
```yaml
translation:
  model: qwen2.5:7b-instruct-q8_0  # 使用的翻译模型
  system_prompt: "你是专业的字幕翻译专家..."  # 系统提示词
  # 其他翻译参数...
```

### 优化配置
```yaml
optimization:
  merge_short_sentences: true  # 是否合并短句
  # 其他优化参数...
```

## 自定义模型

本系统使用两套不同的模型：

### Whisper转录模型
用于视频转录（语音转文字），模型存储在配置的`model_dir`目录中：
- 模型文件夹命名格式：`faster-whisper-模型名`
- 系统会自动去掉"faster-"和"whisper-"前缀显示模型名
- 常见模型：large-v2, large-v3, medium, small等

### Ollama翻译模型  
用于字幕翻译，通过本地Ollama服务提供。您可以通过以下步骤添加自定义翻译模型：

1. 安装Ollama客户端
2. 拉取您需要的模型：
   ```
   ollama pull qwen2.5:7b-instruct
   ```
3. 在应用中使用"刷新模型列表"按钮更新可用模型列表
4. 在设置中选择您的模型用于字幕翻译

## 注意事项

1. **模型区分**：
   - **Whisper模型**：负责转录，将视频音频转为文字
   - **Ollama模型**：负责翻译，将字幕翻译为目标语言
2. 大型视频文件建议使用分段模式处理
3. 翻译功能需要稳定的网络连接（用于下载模型）
4. 处理高清长视频需要较高的CPU、GPU和内存资源
5. 首次使用时请确保已正确配置模型路径

## 开发计划

- [ ] 增加更多语言支持
- [ ] 添加云端翻译API支持
- [ ] 优化大文件处理性能
- [ ] 添加字幕编辑功能
- [ ] 支持更多字幕格式

## 贡献指南

欢迎贡献代码、报告问题或提出建议：

1. Fork 本仓库
2. 创建您的特性分支：`git checkout -b feature/AmazingFeature`
3. 提交您的更改：`git commit -m 'Add some AmazingFeature'`
4. 推送到分支：`git push origin feature/AmazingFeature`
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件

## 致谢

本项目使用了以下开源项目和资源：

- [WhisperX](https://github.com/m-bain/whisperX)
- [Ollama](https://github.com/ollama/ollama)
- [pysrt](https://github.com/byroot/pysrt)
- [tkinter](https://docs.python.org/3/library/tkinter.html)
- [tkinterdnd2](https://github.com/pmgagne/tkinterdnd2)

## 项目结构（更新版）
```
srt/
├── main.py                # 主程序入口
├── optimize_subtitle.py   # 字幕优化模块
├── config.yaml            # 系统配置文件
├── generate_subtitles.py  # 字幕生成模块
├── whisper_config.yaml    # Whisper模型配置
└── Pointless_word.txt     # 无效词过滤列表
```

## 快速开始
```powershell
# 安装依赖
pip install python-dateutil pyyaml

# 处理单个视频
python main.py
输入视频路径：D:\test.mp4
```

## 翻译流程
1. 读取配置文件检查翻译功能是否启用
2. 分批处理字幕（每20条为一批）
3. 为每批添加前后各5条的上下文
4. 调用Ollama API进行批量翻译
5. 清洗翻译结果并保留有效内容
6. 生成带时间轴的双语字幕文件

## 配置说明
1. 编辑`config.yaml`：
```yaml
paths:
  whisper_models: ./models  # 模型存储路径
  output_root: ./processed  # 输出目录

processing:
  video_extensions: [.mp4, .mkv]  # 支持处理的视频格式
  merge_threshold: 2              # 字幕合并阈值(秒)
```

2. 无效词列表配置：
- 编辑`Pointless_word.txt`，每行一个无效词
