import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import tkinter.font as tkFont
import threading
import queue
import time
import sys
from pathlib import Path
import config
from utill import timer, split_sub, is_bilingual_subtitle, is_chinese_subtitle
from typing import List, Optional, Union, Callable, Dict, Any
import traceback
import re
import shutil
import subprocess
import logging

# 检查是否有TkinterDnD拖放支持
try:
    from tkinterdnd2 import TkinterDnD, DND_FILES
    HAS_DRAG_DROP = True
except ImportError:
    HAS_DRAG_DROP = False
    print("警告: TkinterDnD2未安装，拖放功能将不可用")

# 加载配置
cfg = config.load_config()
video_exts = cfg['filetype']['video_extensions']
sub_exts = cfg['filetype']['subtitle_extensions']

class RedirectText:
    """重定向文本输出到Tkinter窗口的类"""
    def __init__(self, text_widget, max_lines=1000):
        self.text_widget = text_widget
        self.queue = queue.Queue()
        self.max_lines = max_lines
        self.current_lines = 0
        self.line_count = 0
        self.lock = threading.Lock()
        self.update_interval = 100  # 更新间隔(毫秒)
        self.scheduled_update = None

    def write(self, string):
        """写入文本到队列，稍后更新UI"""
        with self.lock:
            self.queue.put(string)
            # 如果没有安排更新，安排一个
            if self.scheduled_update is None:
                self.scheduled_update = self.text_widget.after(self.update_interval, self.update_text_widget)
                
    def update_text_widget(self):
        """从队列获取文本并更新文本组件"""
        with self.lock:
            try:
                all_text = ""
                while not self.queue.empty():
                    text = self.queue.get_nowait()
                    all_text += text
                
                if all_text:
                    self.text_widget.configure(state=tk.NORMAL)
                    
                    # 添加文本到窗口
                    self.text_widget.insert(tk.END, all_text)
                    
                    # 计算当前行数
                    content = self.text_widget.get("1.0", tk.END)
                    new_line_count = content.count('\n')
                    
                    # 如果超过最大行数，删除旧行
                    if new_line_count > self.max_lines:
                        lines_to_delete = new_line_count - self.max_lines
                        self.text_widget.delete("1.0", f"{lines_to_delete+1}.0")
                    
                    # 滚动到最后
                    self.text_widget.see(tk.END)
                    self.text_widget.configure(state=tk.DISABLED)
                
                # 重置计划的更新
                self.scheduled_update = None
                
                # 如果队列不为空，安排另一个更新
                if not self.queue.empty():
                    self.scheduled_update = self.text_widget.after(self.update_interval, self.update_text_widget)
                    
            except Exception as e:
                print(f"更新日志窗口时出错: {e}")
                self.scheduled_update = None
        
    def flush(self):
        """刷新缓冲区"""
        pass

class ProcessCallback:
    """处理回调类，用于在处理过程中更新UI"""
    def __init__(self, progress_var, status_var, progress_bar, total_files=1, current_file_idx=0):
        self.progress_var = progress_var
        self.status_var = status_var
        self.progress_bar = progress_bar
        self.total_files = max(total_files, 1)  # 避免除以零错误
        self.current_file_idx = current_file_idx
        self.last_update_time = 0
        self.update_interval = 0.1  # 最小更新间隔(秒)
        
    def update(self, progress=None, message=None):
        """更新进度和状态"""
        current_time = time.time()
        
        # 限制更新频率以避免界面卡顿
        if current_time - self.last_update_time < self.update_interval and progress is None:
            return
            
        self.last_update_time = current_time
        
        # 计算总体进度
        if progress is not None:
            # 将当前文件的进度映射到总体进度
            file_weight = 1.0 / self.total_files
            base_progress = self.current_file_idx * file_weight * 100
            current_progress = (progress / 100.0) * file_weight * 100
            total_progress = base_progress + current_progress
            
            # 确保进度不超过100%
            total_progress = min(total_progress, 100)
            
            # 更新进度条
            self.progress_var.set(int(total_progress))
            percentage = f"{int(total_progress)}%"
            self.progress_bar.config(text=percentage)
        
        # 更新状态文本
        if message:
            # 如果有多个文件，添加文件计数
            if self.total_files > 1:
                message = f"[{self.current_file_idx+1}/{self.total_files}] {message}"
            self.status_var.set(message)

class SettingsDialog(tk.Toplevel):
    """设置对话框"""
    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
        self.title("设置")
        self.geometry("700x600")
        self.resizable(True, True)
        
        # 尝试设置图标
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "icon.ico")
            if os.path.exists(icon_path):
                self.iconbitmap(icon_path)
        except Exception as e:
            print(f"设置图标时出错: {e}")
        
        # 配置变量
        self.config = config.load_config()
        self.config_modified = False
        self.settings_saved = False  # 添加一个标志来跟踪是否保存了设置
        
        # 创建UI
        self.create_widgets()
        
        # 绑定关闭事件
        self.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # 居中显示
        self.center_window()
        
    def create_widgets(self):
        """创建对话框组件"""
        # 创建主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Canvas和滚动条
        self.canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        # 设置滚动条
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 创建内容框架
        self.content_frame = ttk.Frame(self.canvas)
        self.canvas_window = self.canvas.create_window((0, 0), window=self.content_frame, anchor=tk.NW)
        
        # 创建底部按钮框架（固定在底部）
        button_frame = ttk.Frame(self)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        
        # 添加按钮
        save_button = ttk.Button(button_frame, text="保存设置", command=self.save_settings, width=15)
        save_button.pack(side=tk.RIGHT, padx=5)
        
        cancel_button = ttk.Button(button_frame, text="取消", command=self.on_close, width=15)
        cancel_button.pack(side=tk.RIGHT, padx=5)
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建各个选项卡
        self.paths_tab = ttk.Frame(self.notebook)
        self.filetypes_tab = ttk.Frame(self.notebook)
        self.whisper_tab = ttk.Frame(self.notebook)
        self.translation_tab = ttk.Frame(self.notebook)
        self.optimize_tab = ttk.Frame(self.notebook)
        
        # 添加选项卡到notebook
        self.notebook.add(self.paths_tab, text="路径设置")
        self.notebook.add(self.filetypes_tab, text="文件类型")
        self.notebook.add(self.whisper_tab, text="Whisper设置")
        self.notebook.add(self.translation_tab, text="翻译设置")
        self.notebook.add(self.optimize_tab, text="优化设置")
        
        # 设置各选项卡内容
        self.setup_paths_tab()
        self.setup_filetypes_tab()
        self.setup_whisper_tab()
        self.setup_translation_tab()
        self.setup_optimize_tab()
        
        # 更新内容框架大小
        self.content_frame.update_idletasks()
        self.canvas.config(scrollregion=self.canvas.bbox(tk.ALL))
        
        # 绑定事件使Canvas可以滚动
        self.content_frame.bind("<Configure>", self.on_frame_configure)
        self.bind_mousewheel()
        
    def on_frame_configure(self, event):
        """当内容框架大小改变时，调整画布的滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def bind_mousewheel(self):
        """绑定鼠标滚轮事件"""
        def _on_mousewheel(event):
            # 检查canvas是否还存在
            try:
                if self.canvas.winfo_exists():
                    # 根据操作系统处理不同的鼠标滚轮事件
                    if sys.platform.startswith("win"):
                        # Windows平台使用delta值
                        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
                    elif event.num == 4 or event.delta > 0:
                        self.canvas.yview_scroll(-1, "units")
                    elif event.num == 5 or event.delta < 0:
                        self.canvas.yview_scroll(1, "units")
            except (tk.TclError, AttributeError):
                # canvas已经被销毁，忽略这个事件
                pass
                
        # 保存事件处理函数的引用，以便后续解绑
        self._mousewheel_handler = _on_mousewheel
        
        # 绑定不同平台的滚轮事件
        if sys.platform.startswith("win"):
            self.bind("<MouseWheel>", self._mousewheel_handler)
        elif sys.platform.startswith("darwin"):
            self.bind("<MouseWheel>", self._mousewheel_handler)
        else:
            self.bind("<Button-4>", self._mousewheel_handler)
            self.bind("<Button-5>", self._mousewheel_handler)
    
    def unbind_mousewheel(self):
        """解绑鼠标滚轮事件"""
        try:
            if sys.platform.startswith("win"):
                self.unbind("<MouseWheel>")
            elif sys.platform.startswith("darwin"):
                self.unbind("<MouseWheel>")
            else:
                self.unbind("<Button-4>")
                self.unbind("<Button-5>")
        except tk.TclError:
            # 如果窗口已经销毁，忽略错误
            pass
    
    def center_window(self):
        """将窗口居中显示"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.parent.winfo_screenwidth() // 2) - (width // 2)
        y = (self.parent.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_paths_tab(self):
        """设置路径选项卡内容"""
        # 创建内容框架
        frame = ttk.Frame(self.paths_tab, padding="10 10 10 10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 路径设置
        path_frame = ttk.LabelFrame(frame, text="路径设置", padding="5 5 5 5")
        path_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Whisper XXL路径
        ttk.Label(path_frame, text="Whisper XXL可执行文件路径:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.whisper_xxl_var = tk.StringVar(value=self.config.get('paths', {}).get('whisper_xxl', ''))
        ttk.Entry(path_frame, textvariable=self.whisper_xxl_var, width=50).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        ttk.Button(
            path_frame, text="浏览...", 
            command=lambda: self.browse_file(self.whisper_xxl_var, "选择Whisper XXL可执行文件", [("可执行文件", "*.exe")])
        ).grid(row=0, column=2, padx=5, pady=5)
        
        # 模型目录
        ttk.Label(path_frame, text="模型目录:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.model_dir_var = tk.StringVar(value=self.config.get('paths', {}).get('model_dir', ''))
        ttk.Entry(path_frame, textvariable=self.model_dir_var, width=50).grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        ttk.Button(
            path_frame, text="浏览...", 
            command=lambda: self.browse_directory(self.model_dir_var, "选择模型目录")
        ).grid(row=1, column=2, padx=5, pady=5)
        
        # 输出目录
        ttk.Label(path_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.output_dir_var = tk.StringVar(value=self.config.get('paths', {}).get('output_dir', 'processed'))
        ttk.Entry(path_frame, textvariable=self.output_dir_var, width=50).grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        ttk.Button(
            path_frame, text="浏览...", 
            command=lambda: self.browse_directory(self.output_dir_var, "选择输出目录")
        ).grid(row=2, column=2, padx=5, pady=5)
        
    def setup_filetypes_tab(self):
        """设置文件类型选项卡内容"""
        # 创建内容框架
        frame = ttk.Frame(self.filetypes_tab, padding="10 10 10 10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 视频文件扩展名
        ttk.Label(frame, text="视频文件扩展名:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.video_extensions_var = tk.StringVar(value=' '.join(self.config.get('filetype', {}).get('video_extensions', [])))
        ttk.Entry(frame, textvariable=self.video_extensions_var, width=50).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 字幕文件扩展名
        ttk.Label(frame, text="字幕文件扩展名:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.subtitle_extensions_var = tk.StringVar(value=' '.join(self.config.get('filetype', {}).get('subtitle_extensions', [])))
        ttk.Entry(frame, textvariable=self.subtitle_extensions_var, width=50).grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 添加帮助标签
        ttk.Label(frame, text="注意: 多个扩展名请用空格分隔，例如: .mp4 .mkv .avi").grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
    def setup_whisper_tab(self):
        """设置Whisper选项卡内容"""
        # 获取Whisper转录模型列表
        installed_models = self.get_whisper_models()
        
        # 创建内容框架
        frame = ttk.Frame(self.whisper_tab, padding="10 10 10 10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 处理模式设置
        process_mode_frame = ttk.LabelFrame(frame, text="处理模式设置", padding="5 5 5 5")
        process_mode_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 处理模式选择
        ttk.Label(process_mode_frame, text="处理模式:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.process_mode_var = tk.StringVar(value=self.config.get('whisper', {}).get('process_mode', 'segment'))
        process_mode_combo = ttk.Combobox(process_mode_frame, textvariable=self.process_mode_var, width=20, state="readonly")
        process_mode_combo['values'] = ['whole', 'segment']
        process_mode_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 整体模式模型选择
        ttk.Label(process_mode_frame, text="整体模式模型:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.whole_model_var = tk.StringVar(value=self.config.get('whisper', {}).get('whole_model', 'large-v2'))
        whole_model_combo = ttk.Combobox(process_mode_frame, textvariable=self.whole_model_var, width=20)
        whole_model_combo['values'] = installed_models
        whole_model_combo.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 分段模式设置
        ttk.Label(process_mode_frame, text="分段长度(秒):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.segment_length_var = tk.StringVar(value=str(self.config.get('whisper', {}).get('segment_length', 960)))
        ttk.Entry(process_mode_frame, textvariable=self.segment_length_var, width=10).grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 分段模式模型选择
        ttk.Label(process_mode_frame, text="首段模型:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.first_segment_model_var = tk.StringVar(value=self.config.get('whisper', {}).get('first_segment_model', 'large-v2'))
        first_segment_model_combo = ttk.Combobox(process_mode_frame, textvariable=self.first_segment_model_var, width=20)
        first_segment_model_combo['values'] = installed_models
        first_segment_model_combo.grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)
        
        ttk.Label(process_mode_frame, text="其他段模型:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.other_segments_model_var = tk.StringVar(value=self.config.get('whisper', {}).get('other_segments_model', 'large-v3-turbo-ct2'))
        other_segments_model_combo = ttk.Combobox(process_mode_frame, textvariable=self.other_segments_model_var, width=20)
        other_segments_model_combo['values'] = installed_models
        other_segments_model_combo.grid(row=4, column=1, padx=5, pady=5, sticky=tk.W)
        
        # Whisper参数设置
        whisper_args_frame = ttk.LabelFrame(frame, text="Whisper参数", padding="5 5 5 5")
        whisper_args_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 模型
        ttk.Label(whisper_args_frame, text="模型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.whisper_model_var = tk.StringVar(value=self.config.get('whisper', {}).get('args', {}).get('model', 'large-v2'))
        whisper_model_combo = ttk.Combobox(whisper_args_frame, textvariable=self.whisper_model_var, width=20)
        whisper_model_combo['values'] = installed_models
        whisper_model_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 语言
        ttk.Label(whisper_args_frame, text="语言:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.whisper_language_var = tk.StringVar(value=self.config.get('whisper', {}).get('args', {}).get('language', 'en'))
        language_combo = ttk.Combobox(whisper_args_frame, textvariable=self.whisper_language_var, width=20)
        language_combo['values'] = ['auto', 'en', 'zh', 'ja', 'ko', 'fr', 'de', 'es', 'it', 'ru']
        language_combo.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 单词时间戳
        ttk.Label(whisper_args_frame, text="单词时间戳:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.word_timestamps_var = tk.StringVar(value=str(self.config.get('whisper', {}).get('args', {}).get('word_timestamps', 'True')))
        word_timestamps_combo = ttk.Combobox(whisper_args_frame, textvariable=self.word_timestamps_var, width=20, state="readonly")
        word_timestamps_combo['values'] = ['True', 'False']
        word_timestamps_combo.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # VAD过滤
        ttk.Label(whisper_args_frame, text="VAD过滤:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.vad_filter_var = tk.StringVar(value=str(self.config.get('whisper', {}).get('args', {}).get('vad_filter', 'True')))
        vad_filter_combo = ttk.Combobox(whisper_args_frame, textvariable=self.vad_filter_var, width=20, state="readonly")
        vad_filter_combo['values'] = ['True', 'False']
        vad_filter_combo.grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)
        
        # VAD方法
        ttk.Label(whisper_args_frame, text="VAD方法:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.vad_method_var = tk.StringVar(value=self.config.get('whisper', {}).get('args', {}).get('vad_method', 'silero'))
        vad_method_combo = ttk.Combobox(whisper_args_frame, textvariable=self.vad_method_var, width=20, state="readonly")
        vad_method_combo['values'] = ['silero', 'auditok', 'webrtc']
        vad_method_combo.grid(row=4, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 输出格式
        ttk.Label(whisper_args_frame, text="输出格式:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.output_format_var = tk.StringVar(value=self.config.get('whisper', {}).get('args', {}).get('output_format', 'srt'))
        output_format_combo = ttk.Combobox(whisper_args_frame, textvariable=self.output_format_var, width=20, state="readonly")
        output_format_combo['values'] = ['srt', 'vtt', 'txt', 'json']
        output_format_combo.grid(row=5, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 最大行宽
        ttk.Label(whisper_args_frame, text="最大行宽:").grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.max_line_width_var = tk.StringVar(value=str(self.config.get('whisper', {}).get('args', {}).get('max_line_width', 50)))
        ttk.Entry(whisper_args_frame, textvariable=self.max_line_width_var, width=10).grid(row=6, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 最大行数
        ttk.Label(whisper_args_frame, text="最大行数:").grid(row=7, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.max_line_count_var = tk.StringVar(value=str(self.config.get('whisper', {}).get('args', {}).get('max_line_count', 3)))
        ttk.Entry(whisper_args_frame, textvariable=self.max_line_count_var, width=10).grid(row=7, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 温度
        ttk.Label(whisper_args_frame, text="温度:").grid(row=8, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.temperature_var = tk.StringVar(value=str(self.config.get('whisper', {}).get('args', {}).get('temperature', 0.0)))
        ttk.Entry(whisper_args_frame, textvariable=self.temperature_var, width=10).grid(row=8, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 初始提示词
        ttk.Label(whisper_args_frame, text="初始提示词:").grid(row=9, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.initial_prompt_var = tk.StringVar(value=self.config.get('whisper', {}).get('args', {}).get('initial_prompt', ''))
        initial_prompt_text = tk.Text(whisper_args_frame, height=4, width=40)
        initial_prompt_text.insert(tk.END, self.initial_prompt_var.get())
        initial_prompt_text.grid(row=9, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 将文本框与变量关联
        def update_initial_prompt(*args):
            self.initial_prompt_var.set(initial_prompt_text.get("1.0", tk.END).strip())
        
        initial_prompt_text.bind("<KeyRelease>", update_initial_prompt)

    def setup_translation_tab(self):
        """设置翻译选项卡内容"""
        # 创建内容框架
        frame = ttk.Frame(self.translation_tab, padding="10 10 10 10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 翻译设置
        translation_frame = ttk.LabelFrame(frame, text="翻译设置", padding="5 5 5 5")
        translation_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 翻译模型
        ttk.Label(translation_frame, text="翻译模型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        # 获取Ollama模型列表（用于翻译）
        installed_models = self.get_ollama_translation_models()
        
        self.translation_model_var = tk.StringVar(value=self.config.get('translation', {}).get('model', ''))
        translation_model_combo = ttk.Combobox(translation_frame, textvariable=self.translation_model_var, width=30)
        translation_model_combo['values'] = installed_models
        translation_model_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 刷新模型列表按钮
        ttk.Button(
            translation_frame, text="刷新模型列表", 
            command=self.refresh_ollama_models
        ).grid(row=0, column=2, padx=5, pady=5)
        
        # 系统提示
        ttk.Label(translation_frame, text="系统提示:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.system_prompt_var = tk.StringVar(value=self.config.get('translation', {}).get('system_prompt', ''))
        system_prompt_text = tk.Text(translation_frame, height=5, width=50)
        system_prompt_text.insert(tk.END, self.system_prompt_var.get())
        system_prompt_text.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky=tk.W)
        
        # 将文本框与变量关联
        def update_system_prompt(*args):
            self.system_prompt_var.set(system_prompt_text.get("1.0", tk.END).strip())
        
        system_prompt_text.bind("<KeyRelease>", update_system_prompt)
        
        # 每批字幕条数
        ttk.Label(translation_frame, text="每批字幕条数:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.batch_size_var = tk.StringVar(value=str(self.config.get('translation', {}).get('batch_size', 5)))
        ttk.Entry(translation_frame, textvariable=self.batch_size_var, width=10).grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 最大重试次数
        ttk.Label(translation_frame, text="最大重试次数:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.max_retries_var = tk.StringVar(value=str(self.config.get('translation', {}).get('max_retries', 3)))
        ttk.Entry(translation_frame, textvariable=self.max_retries_var, width=10).grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 并行处理线程数
        ttk.Label(translation_frame, text="并行处理线程数:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.max_workers_var = tk.StringVar(value=str(self.config.get('translation', {}).get('max_workers', 4)))
        ttk.Entry(translation_frame, textvariable=self.max_workers_var, width=10).grid(row=4, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 添加帮助标签
        ttk.Label(translation_frame, text="注意: 线程数建议根据GPU显存调整").grid(row=5, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
    
    def setup_optimize_tab(self):
        """设置优化选项卡内容"""
        # 创建内容框架
        frame = ttk.Frame(self.optimize_tab, padding="10 10 10 10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 优化设置
        optimize_frame = ttk.LabelFrame(frame, text="字幕优化设置", padding="5 5 5 5")
        optimize_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 最短句子长度
        ttk.Label(optimize_frame, text="最短句子长度:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.min_sentence_length_var = tk.StringVar(value=str(self.config.get('optimize', {}).get('min_sentence_length', 8)))
        ttk.Entry(optimize_frame, textvariable=self.min_sentence_length_var, width=10).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 合并间隔时间
        ttk.Label(optimize_frame, text="合并间隔时间(秒):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.merge_interval_var = tk.StringVar(value=str(self.config.get('optimize', {}).get('merge_interval', 1.5)))
        ttk.Entry(optimize_frame, textvariable=self.merge_interval_var, width=10).grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 去除无意义词
        ttk.Label(optimize_frame, text="去除无意义词:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.remove_meaningless_var = tk.BooleanVar(value=self.config.get('optimize', {}).get('remove_meaningless', True))
        ttk.Checkbutton(optimize_frame, variable=self.remove_meaningless_var).grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 无意义词列表框架
        meaningless_words_frame = ttk.LabelFrame(frame, text="无意义词列表", padding="5 5 5 5")
        meaningless_words_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 无意义词列表文本框
        meaningless_words_scrollbar = ttk.Scrollbar(meaningless_words_frame)
        meaningless_words_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.meaningless_words_text = tk.Text(meaningless_words_frame, height=10, width=60, yscrollcommand=meaningless_words_scrollbar.set)
        self.meaningless_words_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        meaningless_words_scrollbar.config(command=self.meaningless_words_text.yview)
        
        # 填充无意义词列表
        meaningless_words = self.config.get('optimize', {}).get('meaningless_words', [])
        
        # 如果配置中没有无意义词，尝试从Pointless_word.txt文件加载
        if not meaningless_words:
            pointless_file = 'Pointless_word.txt'
            if os.path.exists(pointless_file):
                try:
                    with open(pointless_file, 'r', encoding='utf-8') as f:
                        meaningless_words = [line.strip() for line in f if line.strip()]
                    print(f"从 {pointless_file} 加载了 {len(meaningless_words)} 个无意义词到设置中")
                except Exception as e:
                    print(f"读取 {pointless_file} 时出错: {e}")
        
        self.meaningless_words_text.insert(tk.END, '\n'.join(meaningless_words))
        
        # 添加帮助标签
        ttk.Label(meaningless_words_frame, text="每行一个词或短语").pack(anchor=tk.W, padx=5, pady=5)
    
    def browse_file(self, var, title, filetypes):
        """浏览文件对话框"""
        file_path = filedialog.askopenfilename(title=title, filetypes=filetypes)
        if file_path:
            var.set(file_path)
            self.config_modified = True
    
    def browse_directory(self, var, title):
        """浏览目录对话框"""
        directory = filedialog.askdirectory(title=title)
        if directory:
            var.set(directory)
            self.config_modified = True
    
    def refresh_ollama_models(self):
        """刷新模型列表"""
        try:
            self.status_var = tk.StringVar(value="正在刷新模型列表...")
            status_label = ttk.Label(self.translation_tab, textvariable=self.status_var)
            status_label.pack(side=tk.BOTTOM, pady=5)
            
            # 创建线程运行命令
            def run_command():
                try:
                    # 使用get_ollama_models获取模型列表
                    installed_models = self.get_ollama_translation_models()
                    
                    # 在主线程更新UI
                    self.after(0, lambda: self.update_model_list(installed_models))
                except Exception as e:
                    error = f"刷新模型列表时出错: {e}"
                    self.after(0, lambda: self.status_var.set(error))
            
            threading.Thread(target=run_command, daemon=True).start()
            
        except Exception as e:
            if hasattr(self, 'status_var'):
                self.status_var.set(f"刷新模型列表时出错: {e}")
            else:
                print(f"刷新模型列表时出错: {e}")
    
    def update_model_list(self, models):
        """更新模型列表"""
        if hasattr(self, 'translation_model_var'):
            # 更新翻译模型列表
            for child in self.translation_tab.winfo_children():
                if isinstance(child, ttk.Frame):
                    for frame_child in child.winfo_children():
                        if isinstance(frame_child, ttk.LabelFrame):
                            for combo in frame_child.winfo_children():
                                if isinstance(combo, ttk.Combobox):
                                    combo_name = str(combo.cget("textvariable"))
                                    if "translation_model_var" in combo_name:
                                        combo['values'] = models
                                        if hasattr(self, 'status_var'):
                                            self.status_var.set(f"已刷新模型列表，找到 {len(models)} 个模型")
            return
        
            if hasattr(self, 'status_var'):
                self.status_var.set("无法找到翻译模型下拉框")
    
    def save_settings(self):
        """保存设置"""
        try:
            # 备份当前配置文件
            config_path = config.get_config_path()
            backup_path = f"{config_path}.bak"
            
            try:
                # 如果备份文件已存在，先删除
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                shutil.copy2(config_path, backup_path)
            except Exception as e:
                print(f"备份配置文件时出错: {e}")
            
            # 更新配置
            # 路径设置
            self.config.setdefault('paths', {})
            self.config['paths']['whisper_xxl'] = self.whisper_xxl_var.get()
            self.config['paths']['model_dir'] = self.model_dir_var.get()
            self.config['paths']['output_dir'] = self.output_dir_var.get()
            
            # 文件类型设置
            self.config.setdefault('filetype', {})
            self.config['filetype']['video_extensions'] = [ext.strip() for ext in self.video_extensions_var.get().split() if ext.strip()]
            self.config['filetype']['subtitle_extensions'] = [ext.strip() for ext in self.subtitle_extensions_var.get().split() if ext.strip()]
            
            # Whisper设置
            self.config.setdefault('whisper', {})
            self.config['whisper']['process_mode'] = self.process_mode_var.get()
            self.config['whisper']['whole_model'] = self.whole_model_var.get()
            self.config['whisper']['segment_length'] = int(self.segment_length_var.get())
            self.config['whisper']['first_segment_model'] = self.first_segment_model_var.get()
            self.config['whisper']['other_segments_model'] = self.other_segments_model_var.get()
            
            # Whisper参数
            self.config['whisper'].setdefault('args', {})
            self.config['whisper']['args']['model'] = self.whisper_model_var.get()
            self.config['whisper']['args']['language'] = self.whisper_language_var.get()
            self.config['whisper']['args']['word_timestamps'] = self.word_timestamps_var.get()
            self.config['whisper']['args']['vad_filter'] = self.vad_filter_var.get()
            self.config['whisper']['args']['vad_method'] = self.vad_method_var.get()
            self.config['whisper']['args']['output_format'] = self.output_format_var.get()
            self.config['whisper']['args']['max_line_width'] = int(self.max_line_width_var.get())
            self.config['whisper']['args']['max_line_count'] = int(self.max_line_count_var.get())
            self.config['whisper']['args']['temperature'] = float(self.temperature_var.get())
            self.config['whisper']['args']['initial_prompt'] = self.initial_prompt_var.get()
            
            # 翻译设置
            self.config.setdefault('translation', {})
            self.config['translation']['model'] = self.translation_model_var.get()
            self.config['translation']['system_prompt'] = self.system_prompt_var.get()
            self.config['translation']['batch_size'] = int(self.batch_size_var.get())
            self.config['translation']['max_retries'] = int(self.max_retries_var.get())
            self.config['translation']['max_workers'] = int(self.max_workers_var.get())
            
            # 优化设置
            self.config.setdefault('optimize', {})
            self.config['optimize']['min_sentence_length'] = int(self.min_sentence_length_var.get())
            self.config['optimize']['merge_interval'] = float(self.merge_interval_var.get())
            self.config['optimize']['remove_meaningless'] = self.remove_meaningless_var.get()
            
            # 获取无意义词列表
            meaningless_words_text = self.meaningless_words_text.get("1.0", tk.END).strip()
            meaningless_words = [word.strip() for word in meaningless_words_text.split('\n') if word.strip()]
            self.config['optimize']['meaningless_words'] = meaningless_words
            
            # 同时保存到Pointless_word.txt文件以保持同步
            try:
                pointless_file = 'Pointless_word.txt'
                with open(pointless_file, 'w', encoding='utf-8') as f:
                    for word in meaningless_words:
                        f.write(word + '\n')
                print(f"已同步保存 {len(meaningless_words)} 个无意义词到 {pointless_file}")
            except Exception as e:
                print(f"保存无意义词到文件时出错: {e}")
            
            # 保存配置
            config.save_config(self.config)
            
            messagebox.showinfo("成功", "设置已保存")
            self.settings_saved = True  # 设置保存成功标志
            self.config_modified = False
            self.destroy()
            
        except Exception as e:
            error_msg = f"保存设置时出错: {e}\n{traceback.format_exc()}"
            print(error_msg)
            messagebox.showerror("错误", f"保存设置时出错: {str(e)}")
    
    def on_close(self):
        """关闭对话框时的处理"""
        # 关闭前先解绑鼠标滚轮
        self.unbind_mousewheel()
        
        # 直接关闭对话框，不保存设置
        self.destroy()

    def get_ollama_translation_models(self):
        """获取Ollama翻译模型列表"""
        models = []
        try:
            import subprocess
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:  # 跳过标题行
                    for line in lines[1:]:
                        if line.strip():
                            # 第一列是模型名称
                            model_name = line.split()[0]
                            if model_name and ':' in model_name:
                                models.append(model_name)
            
            if not models:
                # 如果没有找到模型，添加一些常见模型作为默认选项
                models = ["qwen:latest", "phi3:latest", "mistral:latest", "llama3:latest"]
                print("未找到已安装的Ollama模型，使用默认列表")
        except Exception as e:
            print(f"获取Ollama模型列表时出错: {e}")
            models = ["qwen:latest", "phi3:latest", "mistral:latest", "llama3:latest"]
        
        return models
    
    def get_whisper_models(self):
        """获取Whisper转录模型列表"""
        models = []
        
        # 从模型目录获取Whisper模型列表
        model_dir = self.config.get('paths', {}).get('model_dir', '')
        if model_dir and os.path.exists(model_dir):
            try:
                # 从模型目录获取模型名称
                model_folders = [f for f in os.listdir(model_dir) if os.path.isdir(os.path.join(model_dir, f))]
                for folder in model_folders:
                    model_name = folder
                    # 去掉"faster-"前缀
                    model_name = model_name.replace("faster-", "")
                    # 去掉"whisper-"
                    model_name = model_name.replace("whisper-", "")
                    
                    if model_name:  # 确保处理后的名称不为空
                        models.append(model_name)
                
                print(f"从模型目录获取到 {len(models)} 个Whisper模型")
            except Exception as e:
                print(f"从模型目录获取Whisper模型列表时出错: {e}")
        
        # 如果没有找到模型，使用默认列表
        if not models:
            print("未从模型目录获取到Whisper模型，使用默认列表")
            models = ["large-v2", "large-v3", "medium", "small"]
        
        return models

class SubtitleProcessorApp:
    """主应用类"""
    def __init__(self, root=None):
        if root is None:
            # 根据是否有拖放支持，创建不同的根窗口
            if HAS_DRAG_DROP:
                self.root = TkinterDnD.Tk()
            else:
                self.root = tk.Tk()
        else:
            self.root = root
            
        # 设置窗口标题和图标
        self.root.title("视频字幕处理工具")
        self.root.geometry("900x600")
        
        # 尝试设置图标
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            print(f"设置图标时出错: {e}")
        
        # 创建UI变量
        self.progress_var = tk.IntVar(value=0)
        self.status_var = tk.StringVar(value="就绪")
        self.mode_var = tk.StringVar(value="full")
        
        # 文件列表
        self.files_to_process = []
        
        # 处理线程
        self.processing_thread = None
        self.stop_flag = False
        print(f"DEBUG: 初始化停止标志，类型: {type(self.stop_flag)}, 值: {self.stop_flag}")
        
        # 创建UI组件
        self.create_widgets()
        
        # 配置拖放支持
        if HAS_DRAG_DROP:
            self.setup_drag_drop()
        
        # 重定向stdout和stderr到文本窗口
        self.redirect_stdout_stderr()
        
    def create_widgets(self):
        """创建UI组件"""
        # 使用网格布局
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(3, weight=1)
        
        # 创建顶部按钮框架
        button_frame = ttk.Frame(self.root, padding="5 5 5 5")
        button_frame.grid(row=0, column=0, sticky="ew")
        
        # 添加按钮
        self.select_file_button = ttk.Button(
            button_frame, text="选择文件", command=self.select_files, width=15
        )
        self.select_file_button.pack(side=tk.LEFT, padx=5)
        
        self.select_dir_button = ttk.Button(
            button_frame, text="选择文件夹", command=self.select_directory, width=15
        )
        self.select_dir_button.pack(side=tk.LEFT, padx=5)
        
        self.clear_button = ttk.Button(
            button_frame, text="清空列表", command=self.clear_file_list, width=15
        )
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        self.settings_button = ttk.Button(
            button_frame, text="设置", command=self.open_settings_dialog, width=15
        )
        self.settings_button.pack(side=tk.LEFT, padx=5)
        
        # 模式选择框架
        mode_frame = ttk.LabelFrame(self.root, text="处理模式", padding="5 5 5 5")
        mode_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=5)
        
        # 添加模式单选按钮
        ttk.Radiobutton(
            mode_frame, text="完整处理（转写+优化+翻译）", variable=self.mode_var, value="full"
        ).pack(side=tk.LEFT, padx=10)
        
        ttk.Radiobutton(
            mode_frame, text="仅转写", variable=self.mode_var, value="transcribe"
        ).pack(side=tk.LEFT, padx=10)
        
        ttk.Radiobutton(
            mode_frame, text="仅优化", variable=self.mode_var, value="optimize"
        ).pack(side=tk.LEFT, padx=10)
        
        ttk.Radiobutton(
            mode_frame, text="重新翻译", variable=self.mode_var, value="retranslate"
        ).pack(side=tk.LEFT, padx=10)
        
        # 添加拆分双语字幕按钮
        self.split_button = ttk.Button(
            mode_frame, text="拆分双语字幕", command=self.split_bilingual_subtitles, width=15
        )
        self.split_button.pack(side=tk.RIGHT, padx=5)
        
        # 添加合并字幕按钮
        self.merge_button = ttk.Button(
            mode_frame, text="合并字幕", command=self.merge_bilingual_subtitles, width=15
        )
        self.merge_button.pack(side=tk.RIGHT, padx=5)
        
        # 文件列表框架
        file_list_frame = ttk.LabelFrame(self.root, text="文件列表", padding="5 5 5 5")
        file_list_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=5)
        file_list_frame.grid_columnconfigure(0, weight=1)
        
        # 创建文件列表
        self.file_listbox = tk.Listbox(
            file_list_frame, height=8, width=80, selectmode=tk.EXTENDED
        )
        self.file_listbox.grid(row=0, column=0, sticky="ew")
        
        # 添加滚动条
        file_scrollbar = ttk.Scrollbar(file_list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        file_scrollbar.grid(row=0, column=1, sticky="ns")
        self.file_listbox.config(yscrollcommand=file_scrollbar.set)
        
        # 创建中间框架，包含日志框架和计时器框架
        middle_frame = ttk.Frame(self.root)
        middle_frame.grid(row=3, column=0, sticky="nsew", padx=10, pady=5)
        middle_frame.grid_columnconfigure(0, weight=1)
        middle_frame.grid_rowconfigure(0, weight=1)
        
        # 创建日志框架
        log_frame = ttk.LabelFrame(middle_frame, text="处理日志", padding="5 5 5 5")
        log_frame.grid(row=0, column=0, sticky="nsew")
        log_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(0, weight=1)
        
        # 创建日志文本区域
        self.log_text = scrolledtext.ScrolledText(
            log_frame, height=25, width=100, state=tk.DISABLED, wrap=tk.WORD
        )
        self.log_text.grid(row=0, column=0, sticky="nsew")
        
        # 创建定时器显示
        self.timer_label = ttk.Label(middle_frame, text="处理时间: 0:00:00", font=("Helvetica", 10))
        self.timer_label.grid(row=1, column=0, sticky="w", padx=10, pady=5)
        
        # 底部状态框架
        status_frame = ttk.Frame(self.root, padding="5 5 5 5")
        status_frame.grid(row=4, column=0, sticky="ew", padx=10, pady=5)
        status_frame.grid_columnconfigure(0, weight=1)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(
            status_frame, variable=self.progress_var, maximum=100, length=600, mode="determinate"
        )
        self.progress_bar.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        
        # 进度百分比标签
        self.progress_percentage = ttk.Label(status_frame, text="0%", width=5)
        self.progress_percentage.grid(row=0, column=1, padx=5)
        
        # 状态标签
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=0, columnspan=2, sticky="w", padx=5)
        
        # 控制按钮框架
        control_frame = ttk.Frame(self.root, padding="5 5 5 5")
        control_frame.grid(row=5, column=0, sticky="ew", padx=10, pady=5)
        
        # 开始和停止按钮
        self.start_button = ttk.Button(
            control_frame, text="开始处理", command=self.start_processing, width=15
        )
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(
            control_frame, text="停止处理", command=self.stop_processing, width=15, state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # 退出按钮
        self.exit_button = ttk.Button(
            control_frame, text="退出程序", command=self.exit_application, width=15
        )
        self.exit_button.pack(side=tk.RIGHT, padx=5)
    
    def setup_drag_drop(self):
        """配置拖放支持"""
        if HAS_DRAG_DROP:
            self.file_listbox.drop_target_register(DND_FILES)
            self.file_listbox.dnd_bind('<<Drop>>', self.drop_files)
    
    def redirect_stdout_stderr(self):
        """重定向标准输出和错误流到文本窗口"""
        self.stdout_redirect = RedirectText(self.log_text)
        self.stderr_redirect = RedirectText(self.log_text)
        
        sys.stdout = self.stdout_redirect
        sys.stderr = self.stderr_redirect
        
    def drop_files(self, event):
        """处理文件拖放事件"""
        files = self.parse_drop_data(event.data)
        for file_path in files:
            if os.path.exists(file_path):
                if os.path.isfile(file_path):
                    self.add_file(file_path)
                elif os.path.isdir(file_path):
                    self.add_directory(file_path)
    
    def parse_drop_data(self, data):
        """解析拖放数据获取文件列表"""
        if data.startswith('{'):
            # Windows格式
            files = re.findall(r'{([^}]+)}', data)
        else:
            # 标准格式
            files = data.strip().split()
        
        # 去除引号和空格
        return [f.strip('"\'') for f in files]
    
    def select_files(self):
        """打开文件选择对话框"""
        extensions = []
        for ext in video_exts + sub_exts:
            if ext.startswith('.'):
                extensions.append(f"*{ext}")
        else:
                extensions.append(f"*.{ext}")
        
        all_exts = ' '.join(extensions)
        
        file_paths = filedialog.askopenfilenames(
            title="选择要处理的文件",
            filetypes=[
                ("所有支持的文件", all_exts),
                ("视频文件", ' '.join([f"*{ext}" for ext in video_exts])),
                ("字幕文件", ' '.join([f"*{ext}" for ext in sub_exts])),
                ("所有文件", "*.*")
            ]
        )
        
        for file_path in file_paths:
            self.add_file(file_path)
    
    def select_directory(self):
        """打开目录选择对话框"""
        directory = filedialog.askdirectory(title="选择要处理的文件夹")
        if directory:
            self.add_directory(directory)
    
    def add_file(self, file_path):
        """添加文件到列表"""
        if file_path in self.files_to_process:
            print(f"文件已在列表中: {file_path}")
            return
            
        path = Path(file_path)
        if path.suffix.lower() in video_exts + sub_exts:
            self.files_to_process.append(file_path)
            self.file_listbox.insert(tk.END, file_path)
            print(f"已添加文件: {file_path}")
        else:
            print(f"不支持的文件类型: {file_path}")
    
    def add_directory(self, directory):
        """递归添加目录中的所有文件"""
        added_count = 0
        directory_path = Path(directory)
        
        # 获取所有支持的扩展名
        all_extensions = [ext.lower() for ext in video_exts + sub_exts]
        
        # 递归遍历目录
        for file in directory_path.rglob('*'):
            if file.is_file() and file.suffix.lower() in all_extensions:
                if str(file) not in self.files_to_process:
                    self.files_to_process.append(str(file))
                    self.file_listbox.insert(tk.END, str(file))
                    added_count += 1
        
        print(f"已从目录添加 {added_count} 个文件: {directory}")
        
    def clear_file_list(self):
        """清空文件列表"""
        self.files_to_process.clear()
        self.file_listbox.delete(0, tk.END)
        print("已清空文件列表")
    
    def open_settings_dialog(self):
        """打开设置对话框"""
        try:
            # 创建设置对话框
            settings_dialog = SettingsDialog(self.root)
            settings_dialog.title("设置")
            settings_dialog.geometry("800x600")
            settings_dialog.minsize(800, 600)
            
            # 居中窗口
            settings_dialog.center_window()
            
            # 设置为模态对话框
            settings_dialog.transient(self.root)
            settings_dialog.grab_set()
            
            # 等待对话框关闭
            self.root.wait_window(settings_dialog)
            
            # 对话框关闭后，检查是否保存了设置
            if hasattr(settings_dialog, 'settings_saved') and settings_dialog.settings_saved:
                # 重新加载配置
                reload_success = self.reload_config()
                if reload_success:
                    print("设置已更新，翻译模型已重新加载")
                else:
                    print("设置更新失败，请检查配置文件")
            
        except Exception as e:
            print(f"打开设置对话框时出错: {e}")
            # 确保主窗口不被禁用
            self.root.attributes('-disabled', 0)
            self.root.focus_force()
    
    def reload_config(self):
        """重新加载配置"""
        try:
            import config
            import main
            from translator import SubtitleTranslator
            from translat_by_seg import SegmentTranslator
            
            # 清除配置缓存
            config.CONFIG = None
            
            # 重新加载配置
            cfg = config.load_config()
            
            # 获取新的模型名称和并发数
            model_name = cfg.get('translation', {}).get('model', 'qwen:1.5')
            max_workers = cfg.get('translation', {}).get('max_workers', 4)
            
            # 创建新的翻译器
            main.translator = SubtitleTranslator(model_name=model_name, max_workers=max_workers)
            # 重新创建分段翻译器
            main.segment_translator = SegmentTranslator(
                segment_size=20,
                overlap_size=3,
                max_retries=3
            )
            main.model_name = model_name
            main.cfg = cfg
            
            # 更新日志
            logging.info(f"配置已重新加载，翻译模型已更新为: {model_name}")
            return True
        except Exception as e:
            logging.error(f"重新加载配置失败: {str(e)}")
            return False
    
    def split_bilingual_subtitles(self):
        """拆分选中的双语字幕文件"""
        selected_indices = self.file_listbox.curselection()
        if not selected_indices:
            messagebox.showinfo("提示", "请先选择要拆分的字幕文件")
            return
            
        split_count = 0
        for idx in selected_indices:
            file_path = self.files_to_process[idx]
            path = Path(file_path)
            
            if path.suffix.lower() in sub_exts:
                if is_bilingual_subtitle(path):
                    try:
                        original_path, translation_path = split_sub(path)
                        if original_path and translation_path:
                            print(f"成功拆分双语字幕: {path}")
                            print(f"原文字幕: {original_path}")
                            print(f"翻译字幕: {translation_path}")
                            split_count += 1
                        else:
                            print(f"拆分失败: {path}")
                    except Exception as e:
                        print(f"拆分字幕时出错: {e}")
                else:
                    print(f"不是双语字幕文件: {path}")
            else:
                print(f"不是字幕文件: {path}")
        
        if split_count > 0:
            messagebox.showinfo("完成", f"已成功拆分 {split_count} 个字幕文件")
        else:
            messagebox.showinfo("提示", "没有拆分任何字幕文件")
    
    def merge_bilingual_subtitles(self):
        """合并选中的字幕文件（将.srt和.zh.srt合并为双语字幕）"""
        from utill import merge_bilingual_subtitles as merge_subs
        
        selected_indices = self.file_listbox.curselection()
        if not selected_indices:
            messagebox.showinfo("提示", "请先选择要合并的字幕文件")
            return
            
        merge_count = 0
        for idx in selected_indices:
            file_path = self.files_to_process[idx]
            path = Path(file_path)
            
            # 检查是否为支持的字幕文件
            if path.suffix.lower() in sub_exts or path.suffix.lower() == '.zh.srt':
                try:
                    result_path = merge_subs(path)
                    if result_path:
                        print(f"成功合并字幕: {result_path}")
                        merge_count += 1
                    else:
                        print(f"合并失败: {path}")
                except Exception as e:
                    print(f"合并字幕时出错: {e}")
                    traceback.print_exc()
            else:
                print(f"不是字幕文件: {path}")
        
        if merge_count > 0:
            messagebox.showinfo("完成", f"已成功合并 {merge_count} 个字幕文件")
        else:
            messagebox.showinfo("提示", "没有合并任何字幕文件")
    
    def start_processing(self):
        """开始处理文件"""
        if not self.files_to_process:
            messagebox.showinfo("提示", "请先添加要处理的文件")
            return
            
        # 获取当前模式
        mode = self.mode_var.get()
        
        # 禁用开始按钮，启用停止按钮
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # 强制重置停止标志为布尔值
        self.stop_flag = False
        print(f"DEBUG: 重置停止标志，类型: {type(self.stop_flag)}, 值: {self.stop_flag}")
        
        # 重置进度条
        self.progress_var.set(0)
        self.progress_percentage.config(text="0%")
        
        # 启动处理线程
        self.processing_thread = threading.Thread(
            target=self.process_files_thread,
            args=(mode,),
            daemon=True
        )
        self.processing_thread.start()
        
        # 启动计时器
        self.start_time = time.time()
        self.update_timer()
    
    def update_timer(self):
        """更新计时器"""
        if self.processing_thread and self.processing_thread.is_alive():
            elapsed_time = time.time() - self.start_time
            hours, remainder = divmod(int(elapsed_time), 3600)
            minutes, seconds = divmod(remainder, 60)
            time_str = f"处理时间: {hours}:{minutes:02d}:{seconds:02d}"
            self.timer_label.config(text=time_str)
            
            # 每秒更新一次
            self.root.after(1000, self.update_timer)
    
    def stop_processing(self):
        """停止处理文件"""
        if self.processing_thread and self.processing_thread.is_alive():
            self.stop_flag = True
            print(f"DEBUG: 设置停止标志，类型: {type(self.stop_flag)}, 值: {self.stop_flag}")
            self.status_var.set("正在停止...")
            print("正在停止处理...")
    
    def process_files_thread(self, mode):
        """在单独的线程中处理文件"""
        try:
            # 确保停止标志是布尔值
            if not isinstance(self.stop_flag, bool):
                print(f"WARNING: stop_flag 不是布尔值，类型: {type(self.stop_flag)}, 强制重置为 False")
                self.stop_flag = False
            
            print(f"DEBUG: 处理线程开始，stop_flag 类型: {type(self.stop_flag)}, 值: {self.stop_flag}")
            
            # 导入处理文件的函数
            from main import process_single_file
            
            total_files = len(self.files_to_process)
            successful_files = 0
            failed_files = 0
            
            # 初始化回调
            callback = ProcessCallback(
                self.progress_var, 
                self.status_var,
                self.progress_percentage,
                total_files=total_files
            )
            
            # 设置模式参数
            transcribe_only = (mode == "transcribe")
            optimize_only = (mode == "optimize")
            retranslate_mode = (mode == "retranslate")
            
            # 处理每个文件
            for idx, file_path in enumerate(list(self.files_to_process)): # Iterate over a copy
                # 确保停止标志检查使用布尔值
                if isinstance(self.stop_flag, bool) and self.stop_flag:
                    print(f"DEBUG: 停止标志检测到，退出处理循环")
                    break
                elif not isinstance(self.stop_flag, bool):
                    print(f"WARNING: stop_flag 不是布尔值: {type(self.stop_flag)}, 强制重置为 False")
                    self.stop_flag = False
                    
                # Check if the file still exists in the main list (might have been removed by a concurrent operation?)
                # Although unlikely with the current single processing thread, it's safer.
                if file_path not in self.files_to_process:
                    continue

                callback.current_file_idx = idx # Note: idx might not perfectly reflect the original full list if items are removed, but it's used for progress calculation weight.
                callback.update(0, f"开始处理: {Path(file_path).name}")
                
                try:
                    # 创建一个安全的停止标志函数，确保始终返回布尔值
                    def safe_stop_flag():
                        if isinstance(self.stop_flag, bool):
                            return self.stop_flag
                        else:
                            print(f"WARNING: stop_flag 类型异常: {type(self.stop_flag)}, 返回 False")
                            self.stop_flag = False
                            return False
                    
                    print(f"DEBUG: 开始处理文件 {file_path}, stop_flag: {safe_stop_flag()}")
                    print(f"DEBUG: 传递给process_single_file的stopped_flag类型: {type(safe_stop_flag)}")
                    
                    # 处理单个文件
                    result_path = process_single_file(
                        file_path,
                        transcribe_only=transcribe_only,
                        optimize_only=optimize_only,
                        retranslate_mode=retranslate_mode,
                        callback=callback,
                        stopped_flag=safe_stop_flag
                    )
                    
                    if safe_stop_flag(): # Check again after processing
                        print(f"DEBUG: 处理完成后检测到停止标志")
                        break

                    if result_path:
                        successful_files += 1
                        callback.update(100, f"完成处理: {Path(file_path).name}")
                        # Schedule removal from the list in the main thread
                        self.root.after(0, self.remove_processed_file, file_path)
                    else:
                        # Don't increment failed_files if stopped, as it wasn't a processing failure
                        if not safe_stop_flag():
                            failed_files += 1
                            callback.update(100, f"处理失败/跳过: {Path(file_path).name}") # More accurate message
                except Exception as e:
                    # Only count as failure if not stopped
                    if not safe_stop_flag():
                        failed_files += 1
                        error_msg = f"处理文件时出错: {e}\n{traceback.format_exc()}"
                        print(error_msg)
                        callback.update(100, f"处理出错: {Path(file_path).name}")
                    else:
                        print(f"处理被停止，跳过错误记录: {Path(file_path).name}")

            # 处理完成
            remaining_files = len(self.files_to_process) # Get the count of remaining files
            
            # 创建安全的停止标志检查函数
            def final_safe_stop_flag():
                if isinstance(self.stop_flag, bool):
                    return self.stop_flag
                else:
                    print(f"WARNING: 最终检查时 stop_flag 类型异常: {type(self.stop_flag)}, 返回 False")
                    self.stop_flag = False
                    return False
            
            if final_safe_stop_flag():
                self.status_var.set(f"已停止处理。成功: {successful_files}, 失败/跳过: {failed_files}, 剩余: {remaining_files}")
            else:
                # Final progress update only if not stopped
                final_progress = 100 if total_files > 0 else 0
                self.progress_var.set(final_progress)
                self.progress_percentage.config(text=f"{final_progress}%")
                self.status_var.set(f"处理完成。成功: {successful_files}, 失败/跳过: {failed_files}, 剩余: {remaining_files}")

            print(f"\n处理总结! 初始总计: {total_files}, 成功移除: {successful_files}, 失败/跳过: {failed_files}, 最终剩余: {remaining_files}")
            
            # 显示最终消息框
            final_message = ""
            if final_safe_stop_flag():
                final_message = f"处理已手动停止。\n成功处理并移除 {successful_files} 个文件。\n失败或跳过 {failed_files} 个文件。\n剩余 {remaining_files} 个文件未处理。"
            elif failed_files > 0:
                final_message = f"处理完成。\n成功处理并移除 {successful_files} 个文件。\n{failed_files} 个文件处理失败或被跳过，保留在列表中。\n请查看日志了解详情。"
                self.root.after(0, lambda msg=final_message: messagebox.showwarning("处理完成", msg))
            else:
                final_message = f"已成功处理并移除所有 {successful_files} 个文件!"
                self.root.after(0, lambda msg=final_message: messagebox.showinfo("处理完成", msg))

        except ImportError:
             error_msg = "错误：无法导入 main 模块中的 process_single_file。请确保 main.py 在正确的位置并且没有导入错误。"
             print(error_msg)
             self.status_var.set("导入错误")
             self.root.after(0, lambda: messagebox.showerror("导入错误", error_msg))
        except Exception as e:
            error_msg = f"处理过程出错: {e}\n{traceback.format_exc()}"
            print(error_msg)
            self.status_var.set(f"处理出错: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("处理出错", f"处理过程中发生严重错误:\n{str(e)}"))
        
        finally:
            # 恢复按钮状态
            self.root.after(0, lambda: self.start_button.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
    
    def remove_processed_file(self, file_path):
        """从文件列表和Listbox中移除已处理的文件（在主线程中调用）"""
        try:
            # 从内部列表中移除
            if file_path in self.files_to_process:
                self.files_to_process.remove(file_path)

            # 从UI Listbox中移除
            items = self.file_listbox.get(0, tk.END)
            if file_path in items:
                index = items.index(file_path)
                self.file_listbox.delete(index)
                print(f"已从列表移除完成的任务: {file_path}")
            # else:
                # 如果任务启动后，用户清空了列表，这里找不到是正常的
                # print(f"尝试移除任务时未在UI列表中找到: {file_path}")

        except Exception as e:
            print(f"从列表中移除文件时出错 ({file_path}): {e}")
    
    def exit_application(self):
        """退出应用程序"""
        if self.processing_thread and self.processing_thread.is_alive():
            if not messagebox.askyesno("确认退出", "正在处理文件，确定要退出吗？"):
                return
            self.stop_flag = True
            print(f"DEBUG: 退出时设置停止标志，类型: {type(self.stop_flag)}, 值: {self.stop_flag}")
            
        # 恢复标准输出和错误流
        sys.stdout = sys.__stdout__
        sys.stderr = sys.__stderr__
        
        # 退出程序
        self.root.quit()
        self.root.destroy()

def main():
    """主函数"""
    # 根据是否有拖放支持创建根窗口
    if HAS_DRAG_DROP:
        root = TkinterDnD.Tk()
    else:
        root = tk.Tk()
        
    app = SubtitleProcessorApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 