2025-02-21 00:23:26,133 - ERROR - 字幕生成失败: 
命令: C:\Users\<USER>\AppData\Roaming\Subtitle Edit\Whisper\Purfview-Whisper-Faster\faster-whisper-xxl.exe H:\OU\nvg\prev\VIDEO\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4 --model large-v3-turbo --model_dir C:\Users\<USER>\AppData\Roaming\Subtitle Edit\Whisper\Purfview-Whisper-Faster\_models --language en --word_timestamps True --vad_filter True --vad_method pyannote_v3 --output_format srt --output_dir H:\OU\nvg\prev\VIDEO\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc --max_line_width 128 --max_line_count 1 --initial_prompt 请生成准确的时间轴和字幕内容
2025-02-21 00:26:20,489 - ERROR - 字幕生成失败: 
命令: C:\Users\<USER>\AppData\Roaming\Subtitle Edit\Whisper\Purfview-Whisper-Faster\faster-whisper-xxl.exe H:\OU\nvg\prev\VIDEO\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4 --model large-v3-turbo --model_dir C:\Users\<USER>\AppData\Roaming\Subtitle Edit\Whisper\Purfview-Whisper-Faster\_models --language en --word_timestamps True --vad_filter True --vad_method pyannote_v3 --output_format srt --output_dir H:\OU\nvg\prev\VIDEO\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc --max_line_width 128 --max_line_count 1 --initial_prompt 请生成准确的时间轴和字幕内容
2025-02-21 00:32:03,919 - INFO - Executing: "C:\Users\<USER>\AppData\Roaming\Subtitle Edit\Whisper\Purfview-Whisper-Faster\faster-whisper-xxl.exe" "H:\OU\nvg\prev\VIDEO\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4" --model "large-v3-turbo" --model_dir "C:\Users\<USER>\AppData\Roaming\Subtitle Edit\Whisper\Purfview-Whisper-Faster\_models" --language en --device cpu --threads 4 --output_format srt --output_dir "H:\OU\nvg\prev\VIDEO\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc" --initial_prompt 请生成准确的时间轴和字幕内容
2025-02-21 00:35:18,381 - INFO - Executing: "C:\Users\<USER>\AppData\Roaming\Subtitle Edit\Whisper\Purfview-Whisper-Faster\faster-whisper-xxl.exe" "H:\OU\nvg\prev\VIDEO\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4" --model "large-v3-turbo" --model_dir "C:\Users\<USER>\AppData\Roaming\Subtitle Edit\Whisper\Purfview-Whisper-Faster\_models" --language en --device cpu --threads 48 --output_format srt --output_dir "H:\OU\nvg\prev\VIDEO\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc" --initial_prompt 请生成准确的时间轴和字幕内容
2025-02-21 00:42:23,646 - INFO - 开始生成字幕: 18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4
2025-02-21 00:42:23,648 - ERROR - 处理过程中发生异常: [WinError 5] 拒绝访问。
Traceback (most recent call last):
  File "v:\python\srt\generate_subtitles.py", line 52, in generate_subtitle
    result = subprocess.run(
             ^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [WinError 5] 拒绝访问。
2025-02-21 00:42:49,184 - INFO - 开始生成字幕: 18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4
2025-02-21 00:42:49,187 - ERROR - 处理过程中发生异常: [WinError 5] 拒绝访问。
Traceback (most recent call last):
  File "v:\python\srt\generate_subtitles.py", line 52, in generate_subtitle
    result = subprocess.run(
             ^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [WinError 5] 拒绝访问。
2025-02-21 00:43:06,334 - INFO - 开始生成字幕: 18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4
2025-02-21 00:43:06,336 - ERROR - 处理过程中发生异常: [WinError 5] 拒绝访问。
Traceback (most recent call last):
  File "v:\python\srt\generate_subtitles.py", line 52, in generate_subtitle
    result = subprocess.run(
             ^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [WinError 5] 拒绝访问。
2025-02-21 00:43:55,813 - INFO - 开始生成字幕: 18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4
2025-02-21 00:43:55,814 - ERROR - 处理过程中发生异常: [WinError 5] 拒绝访问。
Traceback (most recent call last):
  File "v:\python\srt\generate_subtitles.py", line 52, in generate_subtitle
    result = subprocess.run(
             ^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [WinError 5] 拒绝访问。
2025-02-21 00:44:26,776 - INFO - 开始生成字幕: 18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4
2025-02-21 00:44:26,777 - ERROR - 处理过程中发生异常: [WinError 5] 拒绝访问。
Traceback (most recent call last):
  File "v:\python\srt\generate_subtitles.py", line 52, in generate_subtitle
    result = subprocess.run(
             ^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [WinError 5] 拒绝访问。
2025-02-21 00:45:31,451 - INFO - 开始生成字幕: 18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4
2025-02-21 00:45:31,453 - ERROR - 处理过程中发生异常: [WinError 5] 拒绝访问。
Traceback (most recent call last):
  File "v:\python\srt\generate_subtitles.py", line 52, in generate_subtitle
    result = subprocess.run(
             ^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [WinError 5] 拒绝访问。
2025-02-21 00:45:40,300 - INFO - 开始生成字幕: 18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4
2025-02-21 00:45:40,302 - ERROR - 处理过程中发生异常: [WinError 5] 拒绝访问。
Traceback (most recent call last):
  File "v:\python\srt\generate_subtitles.py", line 52, in generate_subtitle
    result = subprocess.run(
             ^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\ProgramData\anaconda3\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [WinError 5] 拒绝访问。
2025-02-21 00:46:16,625 - INFO - 开始生成字幕: 18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4
2025-02-21 00:46:57,318 - INFO - 开始生成字幕: 18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4
2025-02-21 00:48:17,232 - ERROR - 字幕生成失败: 
命令: C:\Users\<USER>\AppData\Roaming\Subtitle Edit\Whisper\Purfview-Whisper-Faster\faster-whisper-xxl.exe H:\OU\nvg\prev\VIDEO\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc.mp4 --model large-v3-turbo-ct2 --model_dir C:\Users\<USER>\AppData\Roaming\Subtitle Edit\Whisper\Purfview-Whisper-Faster\_models --language zh --word_timestamps True --vad_filter True --vad_method pyannote_v3 --output_format srt --output_dir H:\OU\nvg\prev\VIDEO\18-7-27_Jessy_castingcouch-hd_Bubble Butt Blonde Out Does Her Friend_hevc --max_line_width 128 --max_line_count 1 --initial_prompt 请生成准确的时间轴和字幕内容
2025-02-21 01:37:41,460 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 404 Not Found"
2025-02-21 01:37:41,477 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 404 Not Found"
2025-02-21 01:37:41,494 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 404 Not Found"
2025-02-21 01:37:41,510 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 404 Not Found"
2025-02-21 01:37:41,525 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 404 Not Found"
2025-02-21 01:38:49,320 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:38:49,330 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:38:49,340 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:38:49,350 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:38:49,358 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:41:28,712 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:41:28,722 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:41:28,732 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:41:28,741 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:41:28,750 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:41:37,442 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:41:37,453 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:41:37,462 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:41:37,473 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:41:37,483 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:43:55,206 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:43:55,218 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:43:55,228 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:43:55,238 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:43:55,247 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:44:43,235 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:44:43,246 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:44:43,257 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:44:43,267 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 01:44:43,277 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:09:39,155 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:09:39,185 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:09:39,214 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:09:39,243 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:09:39,261 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:12:53,682 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:12:53,698 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:12:53,712 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:12:53,727 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:12:53,738 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:22:08,745 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:22:08,764 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:22:08,781 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:22:08,798 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:22:08,813 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:04,594 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:04,621 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:04,645 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:04,674 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:04,694 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:52,104 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:52,142 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:52,157 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:52,189 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:52,201 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 02:59:58,013 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-02-21 03:00:19,805 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 03:00:19,835 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 03:00:19,860 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 03:00:19,878 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 03:00:19,914 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 400 Bad Request"
2025-02-21 03:00:25,933 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-02-21 03:01:23,181 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-02-21 03:02:56,370 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-02-22 00:34:37,358 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
2025-02-22 00:35:02,289 - INFO - HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
