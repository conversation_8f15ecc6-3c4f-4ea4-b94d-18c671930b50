import pysrt
import re
import os
from pydub import AudioSegment
from pydub.silence import detect_nonsilent
import wave
import array
import numpy as np
import subprocess
from pathlib import Path
import config

# 尝试导入webrtcvad，如果失败则使用替代方案
try:
    import webrtcvad
    WEBRTCVAD_AVAILABLE = True
except ImportError:
    print("警告: webrtcvad 库无法导入，将使用替代方案进行语音检测")
    WEBRTCVAD_AVAILABLE = False

class SubtitleOptimizer:
    def __init__(self, vad_aggressiveness=3, min_silence_len=500, silence_thresh=-40, min_duration=700, max_duration=5000):
        """
        初始化字幕优化器
        
        参数:
            vad_aggressiveness: VAD敏感度 (0-3)，值越高对语音检测越严格
            min_silence_len: 最小静音长度(ms)，用于检测语音段落
            silence_thresh: 静音阈值(dB)
            min_duration: 最小字幕持续时间(ms)
            max_duration: 最大字幕持续时间(ms)
        """
        self.vad_aggressiveness = vad_aggressiveness
        self.min_silence_len = min_silence_len
        self.silence_thresh = silence_thresh
        self.min_duration = min_duration
        self.max_duration = max_duration
        self.cfg = config.load_config()
        
        # 只在可用时初始化VAD
        if WEBRTCVAD_AVAILABLE:
            self.vad = webrtcvad.Vad(vad_aggressiveness)
        else:
            self.vad = None
    
    def extract_audio(self, video_path, output_path=None):
        """从视频中提取音频"""
        if output_path is None:
            output_path = os.path.splitext(video_path)[0] + ".wav"
        
        if os.path.exists(output_path):
            print(f"音频文件已存在: {output_path}")
            return output_path
            
        print(f"从视频中提取音频: {video_path}")
        ffmpeg_cmd = [
            "ffmpeg", "-i", video_path, 
            "-vn", "-acodec", "pcm_s16le", 
            "-ar", "16000", "-ac", "1", 
            output_path, "-y"
        ]
        
        try:
            subprocess.run(ffmpeg_cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            print(f"音频提取完成: {output_path}")
            return output_path
        except subprocess.CalledProcessError as e:
            print(f"音频提取失败: {e}")
            raise
    
    def detect_speech_segments(self, audio_path):
        """使用VAD和音频分析检测语音段落"""
        print("分析音频中的语音段落...")
        
        # 加载音频
        audio = AudioSegment.from_wav(audio_path)
        
        # 使用pydub检测非静音段落
        print("检测非静音段落...")
        nonsilent_chunks = detect_nonsilent(
            audio, 
            min_silence_len=self.min_silence_len,
            silence_thresh=self.silence_thresh
        )
        
        # 合并过近的段落
        merged_chunks = []
        if nonsilent_chunks:
            current_chunk = list(nonsilent_chunks[0])
            
            for chunk in nonsilent_chunks[1:]:
                # 如果当前块和下一个块之间的间隔小于阈值，则合并
                if chunk[0] - current_chunk[1] < self.min_silence_len:
                    current_chunk[1] = chunk[1]  # 延长当前块
                else:
                    # 添加当前块并开始新块
                    merged_chunks.append(tuple(current_chunk))
                    current_chunk = list(chunk)
            
            # 添加最后一个块
            merged_chunks.append(tuple(current_chunk))
        
        # 如果webrtcvad可用，进一步优化
        refined_segments = []
        
        if WEBRTCVAD_AVAILABLE and self.vad:
            # 打开WAV文件获取参数
            with wave.open(audio_path, 'rb') as wf:
                sample_rate = wf.getframerate()
                sample_width = wf.getsampwidth()
                n_channels = wf.getnchannels()
                
                # 确保音频是16kHz, 16bit, 单声道
                if sample_rate != 16000 or sample_width != 2 or n_channels != 1:
                    print("警告: 音频不是16kHz, 16bit, 单声道格式，VAD可能不准确")
            
            print(f"检测到 {len(merged_chunks)} 个潜在语音段落，正在使用VAD优化...")
            
            # 对每个合并后的块进行VAD分析
            for start_ms, end_ms in merged_chunks:
                # 确保段落不会太短
                if end_ms - start_ms < self.min_duration:
                    # 扩展到最小持续时间
                    center = (start_ms + end_ms) / 2
                    half_min = self.min_duration / 2
                    start_ms = max(0, int(center - half_min))
                    end_ms = int(center + half_min)
                
                # 确保段落不会太长
                if end_ms - start_ms > self.max_duration:
                    # 将长段落分割成多个短段落
                    current_start = start_ms
                    while current_start < end_ms:
                        current_end = min(current_start + self.max_duration, end_ms)
                        refined_segments.append((current_start, current_end))
                        current_start = current_end
                else:
                    refined_segments.append((start_ms, end_ms))
        else:
            # 如果webrtcvad不可用，直接使用pydub的结果进行简单处理
            print(f"检测到 {len(merged_chunks)} 个潜在语音段落，进行简单优化...")
            
            for start_ms, end_ms in merged_chunks:
                # 确保段落不会太短
                if end_ms - start_ms < self.min_duration:
                    center = (start_ms + end_ms) / 2
                    half_min = self.min_duration / 2
                    start_ms = max(0, int(center - half_min))
                    end_ms = int(center + half_min)
                
                # 确保段落不会太长
                if end_ms - start_ms > self.max_duration:
                    # 将长段落分割成多个短段落
                    current_start = start_ms
                    while current_start < end_ms:
                        current_end = min(current_start + self.max_duration, end_ms)
                        refined_segments.append((current_start, current_end))
                        current_start = current_end
                else:
                    refined_segments.append((start_ms, end_ms))
        
        print(f"优化后得到 {len(refined_segments)} 个语音段落")
        return refined_segments
    
    def optimize_subtitles(self, srt_path, video_path=None):
        """优化字幕时间码和断句"""
        print(f"开始优化字幕: {srt_path}")
        
        # 加载字幕
        subs = pysrt.open(srt_path)
        if not subs:
            print("字幕文件为空或无法读取")
            return srt_path
            
        # 如果没有提供视频路径，尝试从字幕路径推断
        if video_path is None:
            base_dir = os.path.dirname(srt_path)
            base_name = os.path.splitext(os.path.basename(srt_path))[0]
            # 移除可能的后缀，如 "_en"
            base_name = re.sub(r'_[a-z]{2}$', '', base_name)
            
            # 尝试常见的视频扩展名
            for ext in ['.mp4', '.mkv', '.avi', '.mov', '.wmv']:
                potential_path = os.path.join(base_dir, base_name + ext)
                if os.path.exists(potential_path):
                    video_path = potential_path
                    print(f"找到对应视频文件: {video_path}")
                    break
            
            if video_path is None:
                print("无法找到对应的视频文件，无法优化时间码")
                return srt_path
        
        # 提取音频
        audio_path = self.extract_audio(video_path)
        
        # 检测语音段落
        speech_segments = self.detect_speech_segments(audio_path)
        
        if not speech_segments:
            print("未检测到语音段落，无法优化时间码")
            return srt_path
            
        # 创建新的字幕文件
        new_subs = pysrt.SubRipFile()
        
        # 如果字幕数量与语音段落数量相近，直接对应调整
        if 0.8 <= len(subs) / len(speech_segments) <= 1.2:
            print("字幕数量与语音段落数量相近，直接对应调整")
            # 使用最少的数量
            min_count = min(len(subs), len(speech_segments))
            
            for i in range(min_count):
                sub = subs[i]
                start_ms, end_ms = speech_segments[i]
                
                # 创建新的字幕项
                new_sub = pysrt.SubRipItem(
                    index=i+1,
                    start=pysrt.SubRipTime(milliseconds=start_ms),
                    end=pysrt.SubRipTime(milliseconds=end_ms),
                    text=sub.text
                )
                new_subs.append(new_sub)
                
            # 如果字幕比语音段落多，添加剩余字幕
            if len(subs) > len(speech_segments):
                last_end_ms = speech_segments[-1][1]
                avg_duration = sum(end-start for start, end in speech_segments) / len(speech_segments)
                
                for i in range(min_count, len(subs)):
                    sub = subs[i]
                    start_ms = last_end_ms + 500  # 添加500ms间隔
                    end_ms = start_ms + avg_duration
                    last_end_ms = end_ms
                    
                    new_sub = pysrt.SubRipItem(
                        index=i+1,
                        start=pysrt.SubRipTime(milliseconds=start_ms),
                        end=pysrt.SubRipTime(milliseconds=end_ms),
                        text=sub.text
                    )
                    new_subs.append(new_sub)
        else:
            # 字幕数量与语音段落数量差异较大，使用文本长度分配
            print("字幕数量与语音段落数量差异较大，使用文本长度分配")
            
            # 计算所有字幕的文本长度总和
            total_text_length = sum(len(sub.text) for sub in subs)
            
            # 计算所有语音段落的总持续时间
            total_duration = sum(end-start for start, end in speech_segments)
            
            # 当前处理的语音段落索引
            current_segment_idx = 0
            current_segment_start, current_segment_end = speech_segments[current_segment_idx]
            current_position = current_segment_start
            
            for i, sub in enumerate(subs):
                # 计算该字幕应占用的时间比例
                text_ratio = len(sub.text) / total_text_length
                sub_duration = int(total_duration * text_ratio)
                
                # 确保最小持续时间
                sub_duration = max(sub_duration, 1000)  # 至少1秒
                
                # 计算结束时间
                sub_end = current_position + sub_duration
                
                # 检查是否超出当前语音段落
                while sub_end > current_segment_end and current_segment_idx < len(speech_segments) - 1:
                    # 移动到下一个语音段落
                    current_segment_idx += 1
                    current_segment_start, current_segment_end = speech_segments[current_segment_idx]
                    
                    # 如果字幕跨越了静音区域，从新段落开始
                    if sub_end > current_segment_start:
                        current_position = current_segment_start
                        sub_end = current_position + sub_duration
                    else:
                        # 否则保持在当前位置
                        pass
                
                # 创建新的字幕项
                new_sub = pysrt.SubRipItem(
                    index=i+1,
                    start=pysrt.SubRipTime(milliseconds=current_position),
                    end=pysrt.SubRipTime(milliseconds=sub_end),
                    text=sub.text
                )
                new_subs.append(new_sub)
                
                # 更新当前位置
                current_position = sub_end
        
        # 保存优化后的字幕
        output_path = os.path.splitext(srt_path)[0] + "_optimized.srt"
        new_subs.save(output_path, encoding='utf-8')
        print(f"字幕优化完成，已保存至: {output_path}")
        
        # 清理临时文件
        if os.path.exists(audio_path) and audio_path != video_path:
            os.remove(audio_path)
            print(f"已删除临时音频文件: {audio_path}")
            
        return output_path

def optimize_subtitle(srt_path, video_path=None):
    """优化字幕的便捷函数"""
    optimizer = SubtitleOptimizer()
    return optimizer.optimize_subtitles(srt_path, video_path)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法: python subtitle_optimizer.py <字幕文件路径> [视频文件路径]")
        sys.exit(1)
        
    srt_path = sys.argv[1]
    video_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    optimize_subtitle(srt_path, video_path) 