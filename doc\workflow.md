# 字幕处理工具工作流程

## 1. 应用启动流程

当应用启动时，执行以下步骤：

1. 加载配置文件(`config.yaml`)
2. 初始化GUI
   - 创建主窗口
   - 设置拖放支持(如果有TkinterDnD模块)
   - 初始化进度条和日志区域
3. 初始化翻译器实例
4. 清理GPU资源，确保没有之前的模型占用内存
5. 显示主界面，等待用户操作

## 2. 字幕生成流程

当选择视频文件进行字幕生成时：

1. 用户选择一个视频文件，并选择"仅视频转写"或"全流程处理"模式
2. 点击"开始处理"按钮
3. 后台线程启动并执行以下操作：
   - 检查配置中的处理模式(整段模式或分段模式)
   - **整段模式**：
     1. 使用配置中指定的整体模型处理完整视频
     2. 生成字幕文件
   - **分段模式**：
     1. 将视频按照配置中的分段长度进行拆分
     2. 使用配置中的不同模型分别处理各个片段
     3. 合并处理结果生成最终字幕文件
4. 进度和日志实时显示在UI中
5. 处理完成后，更新状态并通知用户

## 3. 字幕优化流程

当选择字幕文件进行优化时：

1. 用户选择一个字幕文件，并选择"仅字幕优化"或"全流程处理"模式
2. 点击"开始处理"按钮
3. 后台线程启动并执行以下操作：
   - 检查字幕文件有效性
   - 按照配置文件中的设置优化字幕内容
     - 过滤无效内容
     - 合并短句
     - 调整时间戳
     - 其他优化操作
   - 生成优化后的字幕文件
4. 优化进度和日志实时显示在UI中
5. 处理完成后，更新状态并通知用户

## 4. 字幕翻译流程

当选择字幕文件进行翻译时：

1. 用户选择一个字幕文件，并选择"全流程处理"模式
2. 点击"开始处理"按钮
3. 后台线程启动并执行以下操作：
   - 如果需要，先进行字幕优化
   - 创建子线程池处理翻译任务
   - 按照配置的批处理数量并行翻译字幕
   - 使用配置的模型(如Ollama)和提示词进行翻译
   - 收集翻译结果并生成双语字幕文件
4. 翻译进度和日志实时显示在UI中
5. 处理完成后，清理GPU资源，更新状态并通知用户

## 5. 双语字幕拆分流程

当需要拆分双语字幕时：

1. 用户选择一个双语字幕文件
2. 点击"拆分双语字幕"按钮
3. 后台线程启动并执行以下操作：
   - 检查字幕是否为双语字幕
   - 将字幕拆分为原文字幕和中文字幕两个文件
4. 拆分进度显示在状态栏
5. 处理完成后，显示结果文件路径并通知用户

## 6. 批量处理流程

当选择文件夹进行批量处理时：

1. 用户选择一个文件夹，并选择处理模式
2. 点击"开始处理"按钮
3. 后台线程启动并执行以下操作：
   - 扫描文件夹中的所有文件
   - 根据文件类型(视频或字幕)筛选需要处理的文件
   - 对每个文件执行相应的处理流程
   - 更新总体进度和当前处理的文件信息
4. 批量处理进度和日志实时显示在UI中
5. 所有文件处理完成后，清理资源，更新状态并通知用户

## 7. 设置管理流程

当用户需要修改配置时：

1. 用户点击"设置"按钮
2. 打开设置对话框，包含多个设置标签页：
   - 常规设置：文件类型、合并阈值等基本参数
   - 路径设置：模型目录、输出目录等
   - 语音识别：处理模式、模型选择、分段长度、初始提示词等
   - 翻译设置：翻译模型、批处理数量、系统提示词、用户提示词等
   - 优化设置：过滤阈值、合并规则等
3. 用户修改设置并点击"保存"按钮
4. 设置被保存到配置文件中并重新加载
5. 退出设置对话框，返回主界面

## 8. 资源管理流程

在处理过程中和处理完成后，系统会自动管理资源：

1. 开始处理前，检查GPU资源可用性
2. 处理过程中，监控和显示CPU和GPU使用情况
3. 处理完成后，清理GPU资源：
   - 尝试通过API停止运行中的模型
   - 如果API方法失败，尝试强制结束Ollama进程
4. 处理过程中支持用户中断，中断后同样会执行资源清理

## 9. 进度和日志管理

应用在后台处理过程中提供详细的进度和日志信息：

1. 进度更新通过ProgressCallback类在主线程中安全更新UI
2. 日志输出通过RedirectText类重定向到GUI的文本框
3. 处理时间通过Timer类记录和显示
4. 所有UI更新操作都在主线程中执行，确保UI响应性和线程安全

## 10. 错误处理机制

应用对可能出现的错误进行全面处理：

1. 文件不存在或格式不正确时提供友好提示
2. 处理过程中的异常被捕获并显示在UI中
3. 模型加载失败时提供详细的错误信息
4. 网络问题导致翻译服务中断时有重试机制
5. 处理失败时尝试恢复和清理临时文件
