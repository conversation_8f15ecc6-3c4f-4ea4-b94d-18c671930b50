import os
import config
from pathlib import Path
from typing import Union
from utill import is_video_subtitle, is_subtitle_video,is_bilingual_subtitle,is_chinese_subtitle,timer,split_sub
from generate_subtitles import generate_subtitle
from optimize_subtitle import optimize_srt
from subtitle_quality_optimizer import optimize_subtitle_quality
from translator import SubtitleTranslator
from translat_by_seg import SegmentTranslator
from utill import is_video_subtitle, is_subtitle_video
cfg=config.load_config()
import time
import shutil
import subprocess

video_exts = cfg['filetype']['video_extensions']
sub_exts = cfg['filetype']['subtitle_extensions']
model_name = cfg['translation']['model']
translator = SubtitleTranslator(
    model_name=model_name,  # 使用配置中的模型名称
    max_workers=cfg.get('translation', {}).get('max_workers', 4) # 从配置读取max_workers
)
# 创建分段翻译器实例
segment_translator = SegmentTranslator(
    segment_size=20,
    overlap_size=3, 
    max_retries=3
)


@timer
def process_video(path, callback=None, stopped_flag=None):
    """处理视频文件，生成字幕"""
    if callback:
        callback.update(message=f"正在处理视频: {path.name}")

    # 在调用耗时操作前检查停止标志
    if stopped_flag and stopped_flag():
        print("操作已停止（在视频处理开始前）")
        return None

    if not is_video_subtitle(path):
        # 注意：generate_subtitle也应该接收并使用stopped_flag
        sub_path = generate_subtitle(path, stopped_flag=stopped_flag)
        if stopped_flag and stopped_flag(): # 检查generate_subtitle后是否需要停止
            print("操作已停止（在生成字幕后）")
            return None
        if sub_path:  # 确保返回的不是 None
            return Path(sub_path)  # 将字符串转换为 Path 对象
        return None
    else:
        for ext in sub_exts:
            sub_path = path.with_suffix(ext)
            if os.path.exists(sub_path):
                print(f"视频文件{sub_path.stem}已经存在字幕")
                return sub_path  # 这里已经是 Path 对象
    return None

@timer
def process_subtitle(path, optimize_only=False, retranslate_mode=False, callback=None, stopped_flag=None):
    if stopped_flag and stopped_flag():
        print("操作已停止（在字幕处理开始前）")
        return None

    if is_bilingual_subtitle(path):
        print(f'{path}是双语字幕')
        if callback:
            callback.update(message=f"检测到双语字幕: {Path(path).name}")

        if retranslate_mode:
            # 重新翻译模式
            print(f"重新翻译模式：拆分并重新翻译字幕")
            if callback:
                callback.update(message=f"正在拆分字幕: {Path(path).name}")
            # 注意：split_sub 如果耗时，也应检查 stopped_flag
            original_path, _ = split_sub(path)
            if stopped_flag and stopped_flag(): return None

            if original_path:
                # 优化原文字幕
                if callback:
                    callback.update(30, f"正在优化字幕: {Path(path).name}")
                # 注意：optimize_srt也应该接收并使用stopped_flag
                optimize_srt_path = optimize_srt(original_path, stopped_flag=stopped_flag)
                if stopped_flag and stopped_flag(): return None # 优化后检查

                # 翻译优化后的原文字幕
                if callback:
                    callback.update(60, f"正在翻译字幕: {Path(path).name}")
                # 设置进度回调并使用分段翻译器
                segment_translator.progress_callback = callback
                translate_srt_path = segment_translator.process_file(optimize_srt_path, stopped_flag=stopped_flag)
                if stopped_flag and stopped_flag(): return None # 翻译后检查

                print(f"字幕重新翻译完成: {translate_srt_path}")

                if callback:
                    callback.update(100, f"重新翻译完成: {Path(path).name}")
                return translate_srt_path
            else:
                print(f"拆分字幕失败，无法重新翻译")
                if callback:
                    callback.update(message=f"拆分字幕失败: {Path(path).name}")
                return path
        elif not optimize_only:
            if callback:
                callback.update(30, f"正在修复翻译: {Path(path).name}")
            # 设置进度回调并使用分段翻译器重新翻译
            segment_translator.progress_callback = callback
            result = segment_translator.retranslate_subtitle(path, stopped_flag=stopped_flag)
            if stopped_flag and stopped_flag(): return None # 修复翻译后检查
            if callback:
                callback.update(100, f"翻译修复完成: {Path(path).name}")
            return result if result else path
        else:
            print(f"仅优化模式：跳过双语字幕翻译")
            if callback:
                callback.update(message=f"仅优化模式：跳过双语字幕: {Path(path).name}")
            return path
    elif is_chinese_subtitle(path,strict=True):
        print(f'{path}是中文字幕')
        if callback:
            callback.update(100, f"检测到中文字幕，无需处理: {Path(path).name}")
        return path
    else:
        # 优化字幕
        if callback:
            callback.update(30, f"正在优化字幕: {Path(path).name}")
        # 注意：optimize_srt也应该接收并使用stopped_flag
        optimize_srt_path = optimize_srt(path, stopped_flag=stopped_flag)
        if stopped_flag and stopped_flag(): return None # 优化后检查

        if not optimize_only:
            # 使用分段翻译器进行翻译
            if callback:
                callback.update(60, f"正在翻译字幕: {Path(path).name}")
            # 设置进度回调
            segment_translator.progress_callback = callback
            # 使用分段翻译器
            translate_srt_path = segment_translator.process_file(optimize_srt_path, stopped_flag=stopped_flag)
            if stopped_flag and stopped_flag(): return None # 翻译后检查

            print(f"字幕翻译完成: {translate_srt_path}")

            if callback:
                callback.update(100, f"翻译完成: {Path(path).name}")
            return translate_srt_path
        else:
            print(f"仅优化模式：字幕优化完成 {optimize_srt_path}")

            # 在仅优化模式中，也执行字幕质量优化
            if callback:
                callback.update(80, f"开始字幕质量优化: {Path(path).name}")

            # 执行字幕质量优化
            quality_optimized_path = optimize_subtitle_quality(
                str(optimize_srt_path),
                video_path=None,  # 会自动推断视频路径
                stopped_flag=stopped_flag
            )

            if stopped_flag and stopped_flag():
                return None

            if callback:
                callback.update(100, f"优化完成: {Path(path).name}")
            return quality_optimized_path

    return path # 默认返回原路径，以防万一

@timer
def process_single_file(path:Union[str, Path], transcribe_only:bool=False, optimize_only:bool=False, retranslate_mode:bool=False, callback=None, stopped_flag=None) -> Path:
    path = Path(path)
    result_path = None
    translation_involved = False # 标记是否进行了翻译

    # 检查并修复 stopped_flag 类型问题
    if stopped_flag is not None:
        import threading
        if isinstance(stopped_flag, threading.Event):
            print(f"WARNING: 检测到 stopped_flag 是 threading.Event 对象，已设置: {stopped_flag.is_set()}")
            # 如果已经设置，返回 None；否则创建一个永远返回 False 的函数
            if stopped_flag.is_set():
                print("ERROR: stopped_flag 已被设置，停止处理")
                return None
            else:
                print("WARNING: 将 Event 对象转换为函数")
                stopped_flag = lambda: False
        elif not callable(stopped_flag):
            print(f"WARNING: stopped_flag 不是可调用对象，类型: {type(stopped_flag)}")
            stopped_flag = lambda: False

    if stopped_flag and stopped_flag():
        print("操作已停止（在处理单个文件开始时）")
        return None

    if path.suffix.lower() in sub_exts:
        if transcribe_only:
            print(f"仅转写模式：跳过字幕处理 {path}")
            result_path = path
            if callback:
                callback.update(100, f"跳过字幕处理: {path.name}")
        else:
            # 字幕处理可能涉及翻译
            translation_involved = not optimize_only # 如果不是仅优化，就可能翻译
            result_path = process_subtitle(path, optimize_only, retranslate_mode, callback, stopped_flag)
            if stopped_flag and stopped_flag():
                print("操作已停止（在处理字幕后）")
                # 即使停止了，如果之前加载了模型，也尝试释放内存
                # if translation_involved:
                #     unload_ollama_model(cfg['translation']['model'])
                return None
    elif path.suffix.lower() in video_exts:
        if callback:
            callback.update(message=f"开始处理视频: {path.name}")
        video_srt_path = process_video(path, callback, stopped_flag)
        if stopped_flag and stopped_flag():
            print("操作已停止（在处理视频后）")
            # 视频处理本身不涉及翻译模型加载，无需释放内存
            return None

        if video_srt_path:
            if transcribe_only:
                print(f"仅转写模式：已完成视频转写 {video_srt_path}")
                result_path = video_srt_path
                if callback:
                    callback.update(100, f"视频转写完成: {path.name}")
            else:
                # 等待字幕文件生成并确保可以访问
                max_attempts = 10
                attempt = 0
                video_srt_path = Path(video_srt_path)

                while attempt < max_attempts:
                    if stopped_flag and stopped_flag(): # 在等待循环内部检查
                        print("操作已停止（在等待字幕文件时）")
                        return None

                    if video_srt_path.exists() and video_srt_path.stat().st_size > 0:
                        try:
                            # 尝试打开文件，确保文件已完全写入
                            with open(video_srt_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                if content.strip():  # 确保文件内容不为空
                                    print(f"字幕文件已准备就绪: {video_srt_path}")
                                    break
                                else:
                                    print(f"字幕文件内容为空，等待写入...")
                        except Exception as e:
                            print(f"等待字幕文件可访问: {e}")

                    attempt += 1
                    if callback:
                        callback.update(message=f"等待字幕文件生成，尝试 {attempt}/{max_attempts}")
                    print(f"等待字幕文件生成，尝试 {attempt}/{max_attempts}")
                    time.sleep(3) # 等待期间也应该能响应停止

                if attempt >= max_attempts:
                    print(f"等待字幕文件超时: {video_srt_path}")
                    if callback:
                        callback.update(message=f"等待字幕文件超时: {video_srt_path.name}")
                    return None

                # 开始处理字幕，可能涉及翻译
                if callback:
                    callback.update(message=f"开始处理字幕: {video_srt_path.name}")
                translation_involved = not optimize_only # 如果不是仅优化，就可能翻译
                result_path = process_subtitle(video_srt_path, optimize_only, retranslate_mode, callback, stopped_flag)
                if stopped_flag and stopped_flag():
                    print("操作已停止（在处理视频生成的字幕后）")
                    # if translation_involved:
                    #      unload_ollama_model(cfg['translation']['model'])
                    return None
        else:
            print(f'{path}是视频文件,但未生成字幕文件')
            if callback:
                callback.update(message=f"未能生成字幕文件: {path.name}")
    else:
        # 处理既不是字幕也不是视频的文件
        print(f"跳过不支持的文件类型: {path}")
        if callback:
            callback.update(message=f"跳过不支持的文件: {path.name}")

    # --- 在处理完单个文件后，如果涉及翻译，尝试释放模型内存 ---
    # if translation_involved and not (stopped_flag and stopped_flag()):
    #     unload_ollama_model(cfg['translation']['model'])
    # ----------------------------------------------------

    return result_path

def process_directory(path:Union[str, Path], transcribe_only:bool=False, optimize_only:bool=False, retranslate_mode:bool=False) -> Path:
    # 将path转换为Path对象
    path = Path(path)
    processed_files = []
    
    # 遍历path下的所有文件
    for file in path.rglob('*.*'):
        # 处理单个文件
        result_path = process_single_file(file, transcribe_only, optimize_only, retranslate_mode)
        if result_path:
            processed_files.append(result_path)
    
    print(f"目录处理完成，成功处理 {len(processed_files)} 个文件")
    # 返回path
    return path

def main():
    quit=False
    transcribe_only = False
    optimize_only = False
    retranslate_mode = False
    
    print("欢迎使用字幕处理工具")
    print_current_mode(transcribe_only, optimize_only, retranslate_mode)
    print("输入't'切换到仅转写模式，'o'切换到仅优化模式，'r'切换到重新翻译模式，'f'切换到全流程模式，'q'退出程序")
    
    while quit!='q'.lower():
        path = input("\n请输入文件路径...\n或者输入't'切换模式，'o'切换到仅优化模式，'r'切换到重新翻译模式，'f'切换到全流程模式，'q'退出\n").replace('"','')  # 获取用户输入
        
        if path.lower() == 't':
            transcribe_only = True
            optimize_only = False
            retranslate_mode = False
            print_current_mode(transcribe_only, optimize_only, retranslate_mode)
            continue
        elif path.lower() == 'o':
            transcribe_only = False
            optimize_only = True
            retranslate_mode = False
            print_current_mode(transcribe_only, optimize_only, retranslate_mode)
            continue
        elif path.lower() == 'r':
            transcribe_only = False
            optimize_only = False
            retranslate_mode = True
            print_current_mode(transcribe_only, optimize_only, retranslate_mode)
            continue
        elif path.lower() == 'f':
            transcribe_only = False
            optimize_only = False
            retranslate_mode = False
            print_current_mode(transcribe_only, optimize_only, retranslate_mode)
            continue
        elif path.lower() == 'q':
            return
        elif os.path.isfile(path): 
            process_single_file(path, transcribe_only, optimize_only, retranslate_mode)
        elif os.path.isdir(path): 
            process_directory(path, transcribe_only, optimize_only, retranslate_mode)
        else:
            print("文件路径不存在，请重新输入")
            continue

def print_current_mode(transcribe_only, optimize_only, retranslate_mode):
    """打印当前工作模式"""
    if transcribe_only:
        mode = "仅视频转写"
    elif optimize_only:
        mode = "仅字幕优化"
    elif retranslate_mode:
        mode = "重新翻译(拆分双语字幕并重新翻译)"
    else:
        mode = "全流程处理(转写+优化+翻译)"
    
    print(f"\n当前模式：{mode}")

def unload_ollama_model(model_name: str):
    """尝试释放Ollama模型内存但不删除模型"""
    if not model_name:
        print("未指定要释放内存的Ollama模型名称")
        return

    try:
        # 使用'ollama pull'命令让模型进入休眠状态，释放内存
        # 这样下次使用时不需要重新下载模型，而且不会完全删除模型
        command = ["ollama", "create", "temp-placeholder", "--from", "embed", "--empty"]
        print(f"尝试释放Ollama模型内存: {model_name}...")
        result = subprocess.run(command, capture_output=True, text=True, check=False, encoding='utf-8')

        if result.returncode == 0:
            print(f"成功释放Ollama模型内存: {model_name}")
        else:
            error_message = result.stderr.strip() if result.stderr else result.stdout.strip()
            print(f"释放Ollama模型内存时遇到问题 (返回码: {result.returncode}): {error_message}")
            # 非致命错误，记录后继续
            
        # 删除临时创建的占位模型
        cleanup_cmd = ["ollama", "rm", "temp-placeholder"]
        subprocess.run(cleanup_cmd, capture_output=True, text=True, check=False, encoding='utf-8')

    except FileNotFoundError:
        print("错误：'ollama' 命令未找到。请确保Ollama已安装并添加到系统PATH。")
    except Exception as e:
        print(f"尝试释放Ollama模型内存时发生意外错误: {e}")

if __name__ == "__main__":
    main()
