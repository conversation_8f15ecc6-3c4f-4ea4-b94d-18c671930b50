import subprocess
import os
import time
from pathlib import Path
import config
from datetime import datetime
from utill import timer
import tempfile
import shutil
import re
import threading
# from whisperx_integration import align_existing_subtitle


@timer
def generate_subtitle(video_path, stopped_flag=None):
    subtitle_path = Path(video_path).with_suffix('.srt')
    try:
        # 检查并修复 stopped_flag 类型问题
        if stopped_flag is not None:
            import threading
            if isinstance(stopped_flag, threading.Event):
                print(f"WARNING: generate_subtitle 检测到 stopped_flag 是 threading.Event 对象，已设置: {stopped_flag.is_set()}")
                # 如果已经设置，返回 None；否则创建一个永远返回 False 的函数
                if stopped_flag.is_set():
                    print("ERROR: generate_subtitle stopped_flag 已被设置，停止处理")
                    return None
                else:
                    print("WARNING: generate_subtitle 将 Event 对象转换为函数")
                    stopped_flag = lambda: False
            elif not callable(stopped_flag):
                print(f"WARNING: generate_subtitle stopped_flag 不是可调用对象，类型: {type(stopped_flag)}")
                stopped_flag = lambda: False
        
        # 检查停止标志
        if stopped_flag and stopped_flag():
            print("操作已停止（在生成字幕开始时）")
            return None

        if os.path.exists(subtitle_path):
            print(f"字幕文件已存在: {subtitle_path}")
            return str(subtitle_path)

        # 获取配置
        cfg = config.load_config()

        # 检查处理模式
        process_mode = cfg.get("whisper", {}).get("process_mode", "segment")

        if process_mode == "whole":
            # 整段模式 - 直接处理整个视频
            print(f"使用整段模式处理视频")
            whole_model = cfg.get("whisper", {}).get("whole_model", "large-v3-turbo-ct2")
            print(f"使用模型: {whole_model}")
            # 检查停止标志
            if stopped_flag and stopped_flag(): return None
            result = use_faster_whisper_xxl(video_path, model=whole_model, stopped_flag=stopped_flag)
            # 检查停止标志
            if stopped_flag and stopped_flag(): return None
            if result is None:
                raise Exception("生成字幕失败")
            return result
        else:
            # 分段模式
            print(f"使用分段模式处理视频")
            # 获取视频时长
            duration = get_video_duration(video_path)

            # 获取分段模式的配置
            first_segment_model = cfg.get("whisper", {}).get("first_segment_model", "large-v2")
            other_segments_model = cfg.get("whisper", {}).get("other_segments_model", "large-v3-turbo-ct2")
            segment_length = cfg.get("whisper", {}).get("segment_length", 15 * 60)  # 默认15分钟

            print(f"分段模式配置: 第一段使用{first_segment_model}，其他段使用{other_segments_model}，第一段长度{segment_length/60}分钟")

            # 如果视频时长小于设定的分段长度，直接处理
            if duration <= segment_length:
                print(f"视频时长为 {duration/60:.2f} 分钟，小于分段长度，直接使用{first_segment_model}处理")
                # 检查停止标志
                if stopped_flag and stopped_flag(): return None
                result = use_faster_whisper_xxl(video_path, model=first_segment_model, stopped_flag=stopped_flag)
                # 检查停止标志
                if stopped_flag and stopped_flag(): return None
                if result is None:
                    raise Exception("生成字幕失败")
                return result

            # 否则，只将视频分为两段：前段和后段
            print(f"视频时长为 {duration/60:.2f} 分钟，将分为前后两段处理")

            # 创建临时目录存放分割的视频和字幕
            temp_dir = tempfile.mkdtemp()
            print(f"创建临时目录: {temp_dir}")

            try:
                # 分割视频成两段：前段（0到segment_length）和后段（segment_length到结束）
                segment_srt_files = []

                # 处理第一段（0到segment_length）
                first_segment_path = os.path.join(temp_dir, "segment_first.mp4")
                # 检查停止标志
                if stopped_flag and stopped_flag(): return None
                split_video(video_path, first_segment_path, 0, segment_length)

                # 确保视频分割成功
                if not os.path.exists(first_segment_path) or os.path.getsize(first_segment_path) == 0:
                    raise Exception(f"视频第一段分割失败或为空")

                print(f"使用{first_segment_model}模型处理第一段视频...")
                # 检查停止标志
                if stopped_flag and stopped_flag(): return None
                first_segment_srt = use_faster_whisper_xxl(first_segment_path, model=first_segment_model, stopped_flag=stopped_flag)
                # 检查停止标志
                if stopped_flag and stopped_flag(): return None

                # 检查字幕文件是否存在
                if first_segment_srt is None or not os.path.exists(first_segment_srt):
                    print(f"警告: 未找到第一段字幕文件，尝试查找替代文件...")
                    first_segment_srt = find_subtitle_in_directory(Path(first_segment_path).parent, Path(first_segment_path).stem)
                    if first_segment_srt is None or not os.path.exists(first_segment_srt):
                        raise Exception(f"无法找到第一段字幕文件")

                print(f"找到第一段字幕文件: {first_segment_srt}")
                first_adjusted_srt = os.path.join(temp_dir, "adjusted_segment_first.srt")
                # 第一段无需调整时间戳
                adjust_subtitle_timestamps(first_segment_srt, first_adjusted_srt, 0)
                segment_srt_files.append(first_adjusted_srt)

                # 处理后段（segment_length到结束）
                if duration > segment_length:
                    second_segment_path = os.path.join(temp_dir, "segment_second.mp4")
                    second_segment_duration = duration - segment_length
                    # 检查停止标志
                    if stopped_flag and stopped_flag(): return None
                    split_video(video_path, second_segment_path, segment_length, second_segment_duration)

                    # 确保视频分割成功
                    if not os.path.exists(second_segment_path) or os.path.getsize(second_segment_path) == 0:
                        raise Exception(f"视频后段分割失败或为空")

                    print(f"使用{other_segments_model}模型处理后段视频...")
                    # 检查停止标志
                    if stopped_flag and stopped_flag(): return None
                    second_segment_srt = use_faster_whisper_xxl(second_segment_path, model=other_segments_model, stopped_flag=stopped_flag)
                    # 检查停止标志
                    if stopped_flag and stopped_flag(): return None

                    # 检查字幕文件是否存在
                    if second_segment_srt is None or not os.path.exists(second_segment_srt):
                        print(f"警告: 未找到后段字幕文件，尝试查找替代文件...")
                        second_segment_srt = find_subtitle_in_directory(Path(second_segment_path).parent, Path(second_segment_path).stem)
                        if second_segment_srt is None or not os.path.exists(second_segment_srt):
                            raise Exception(f"无法找到后段字幕文件")

                    print(f"找到后段字幕文件: {second_segment_srt}")
                    second_adjusted_srt = os.path.join(temp_dir, "adjusted_segment_second.srt")
                    # 后段需要调整时间戳，加上第一段的时长
                    adjust_subtitle_timestamps(second_segment_srt, second_adjusted_srt, segment_length)
                    segment_srt_files.append(second_adjusted_srt)

                if not segment_srt_files:
                    raise Exception("没有成功生成任何字幕片段")

                # 合并所有字幕文件
                merge_subtitle_files(segment_srt_files, subtitle_path)
                print(f"所有字幕片段已合并: {subtitle_path}")

                # 确保最终文件存在且不为空
                if not os.path.exists(subtitle_path) or os.path.getsize(subtitle_path) == 0:
                    raise Exception("生成的字幕文件不存在或为空")

                return str(subtitle_path)

            finally:
                # 清理临时目录
                try:
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
                        print(f"临时目录已删除: {temp_dir}")
                except Exception as e:
                    print(f"清理临时文件时出错: {str(e)}")

    except Exception as e:
        print(f"生成字幕时出错: {str(e)}")
        if os.path.exists(subtitle_path):
            try:
                os.remove(subtitle_path)
                print(f"已删除不完整的字幕文件: {subtitle_path}")
            except:
                pass
        return None

def get_video_duration(video_path):
    """获取视频时长（秒）"""
    cmd = [
        "ffprobe", 
        "-v", "error", 
        "-show_entries", "format=duration", 
        "-of", "default=noprint_wrappers=1:nokey=1", 
        str(video_path)
    ]
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    return float(result.stdout.strip())

def split_video(input_path, output_path, start_time, duration):
    """分割视频"""
    cmd = [
        "ffmpeg",
        "-i", str(input_path),
        "-ss", str(start_time),
        "-t", str(duration),
        "-c", "copy",
        "-y",
        str(output_path)
    ]
    print(f"分割视频: {os.path.basename(output_path)}")
    subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

def adjust_subtitle_timestamps(input_srt, output_srt, offset_seconds):
    """调整字幕时间戳，加上偏移量"""
    with open(input_srt, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 时间戳格式: 00:00:00,000 --> 00:00:00,000
    pattern = r'(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})'
    
    def add_offset(match):
        # 解析时间
        h1, m1, s1, ms1 = map(int, match.groups()[:4])
        h2, m2, s2, ms2 = map(int, match.groups()[4:])
        
        # 转换为毫秒并添加偏移
        time1_ms = h1 * 3600000 + m1 * 60000 + s1 * 1000 + ms1 + offset_seconds * 1000
        time2_ms = h2 * 3600000 + m2 * 60000 + s2 * 1000 + ms2 + offset_seconds * 1000
        
        # 转回时:分:秒,毫秒格式
        new_h1, remainder = divmod(time1_ms, 3600000)
        new_m1, remainder = divmod(remainder, 60000)
        new_s1, new_ms1 = divmod(remainder, 1000)
        
        new_h2, remainder = divmod(time2_ms, 3600000)
        new_m2, remainder = divmod(remainder, 60000)
        new_s2, new_ms2 = divmod(remainder, 1000)
        
        return f"{new_h1:02d}:{new_m1:02d}:{new_s1:02d},{new_ms1:03d} --> {new_h2:02d}:{new_m2:02d}:{new_s2:02d},{new_ms2:03d}"
    
    adjusted_content = re.sub(pattern, add_offset, content)
    
    with open(output_srt, 'w', encoding='utf-8') as f:
        f.write(adjusted_content)

def merge_subtitle_files(srt_files, output_path):
    """合并多个SRT字幕文件"""
    subtitle_index = 1
    with open(output_path, 'w', encoding='utf-8') as outfile:
        for srt_file in srt_files:
            with open(srt_file, 'r', encoding='utf-8') as infile:
                content = infile.read()
                
                # 替换字幕索引
                lines = content.split('\n')
                i = 0
                while i < len(lines):
                    if lines[i].strip().isdigit():
                        lines[i] = str(subtitle_index)
                        subtitle_index += 1
                        i += 3  # 跳过时间戳和字幕内容
                    i += 1
                
                outfile.write('\n'.join(lines) + '\n\n')

def use_faster_whisper_xxl(video_path, model="large-v3-turbo-ct2", stopped_flag=None):
    """使用传统Whisper生成字幕"""
    temp_dir = None
    try:
        # 检查停止标志
        if stopped_flag and stopped_flag():
            print("操作已停止（在运行Whisper XXL之前）")
            return None

        video_path = Path(video_path).resolve()
        if not video_path.exists():
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        # 创建临时目录用于输出，避免长路径和特殊字符问题
        temp_dir = tempfile.mkdtemp(prefix="whisper_")
        print(f"创建临时输出目录: {temp_dir}")
        
        # 创建一个安全的临时文件名
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', video_path.stem)
        safe_name = safe_name[:50]  # 限制长度
        temp_video_name = f"{safe_name}.{video_path.suffix[1:]}"
        
        # 最终输出路径
        final_output_path = video_path.with_suffix('.srt')
        temp_output_path = Path(temp_dir) / f"{safe_name}.srt"
        
        # 构建命令，使用临时目录
        command = build_whisper_command(video_path, model, temp_dir)

        print(f"开始生成字幕，使用模型: {model}...")
        print(f"临时输出目录: {temp_dir}")
        
        # 运行Whisper进程
        process = run_whisper_process(command, stopped_flag=stopped_flag)

        # 检查停止标志
        if stopped_flag and stopped_flag():
            print("操作已停止（在运行Whisper XXL之后）")
            if process and process.poll() is None:
                try:
                    process.terminate()
                    print("尝试终止 Whisper 进程...")
                except Exception as term_err:
                    print(f"终止 Whisper 进程时出错: {term_err}")
            return None

        if process is None:
            print("Whisper进程执行失败")
            return None

        # 在临时目录中查找生成的字幕文件（即使进程返回错误码）
        temp_srt_path = find_subtitle_in_directory(temp_dir, video_path.stem)
        if temp_srt_path is None:
            # 尝试查找任何.srt文件
            srt_files = list(Path(temp_dir).glob("*.srt"))
            if srt_files:
                temp_srt_path = str(srt_files[0])
                print(f"找到临时字幕文件: {temp_srt_path}")
            else:
                print(f"错误: 在临时目录中未找到字幕文件")
                # 即使没找到文件，也检查进程返回码
                if process.returncode == 3221226505:
                    print("虽然是栈缓冲区溢出错误，但未找到字幕文件")
                return None
        
        # 检查文件是否有效（非空）
        if os.path.getsize(temp_srt_path) == 0:
            print(f"警告: 字幕文件为空: {temp_srt_path}")
            return None
        
        # 将字幕文件移动到最终位置
        try:
            shutil.move(temp_srt_path, final_output_path)
            print(f"字幕文件已移动到: {final_output_path}")
            
            # 即使进程返回错误码，如果文件成功保存就认为成功
            if process.returncode == 3221226505:
                print("尽管出现栈缓冲区溢出错误，字幕文件已成功保存")
            
            return str(final_output_path)
        except Exception as move_err:
            print(f"移动字幕文件时出错: {move_err}")
            # 尝试复制
            try:
                shutil.copy2(temp_srt_path, final_output_path)
                print(f"字幕文件已复制到: {final_output_path}")
                return str(final_output_path)
            except Exception as copy_err:
                print(f"复制字幕文件时出错: {copy_err}")
                return None

    except Exception as e:
        print(f"使用Whisper XXL时出错: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # 清理临时目录
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print(f"临时目录已清理: {temp_dir}")
            except Exception as cleanup_err:
                print(f"清理临时目录时出错: {cleanup_err}")

def build_whisper_command(video_path, model, temp_output_dir=None):
    """
    构建Whisper命令行参数
    
    注意：这里的model参数是动态传入的，会覆盖配置文件中的whisper.args.model参数。
    根据处理模式的不同，会传入不同的模型：
    - 整段模式：使用whisper.whole_model参数值
    - 分段模式：第一段使用whisper.first_segment_model，其他段使用whisper.other_segments_model
    
    设置界面中"Whisper参数"部分的模型选择仅作为默认配置，实际会被上述特定模型设置覆盖。
    """
    cfg = config.load_config()
    
    # 使用临时输出目录或原始目录
    output_dir = temp_output_dir if temp_output_dir else str(video_path.parent)
    
    command = [
        f"{cfg['paths']['whisper_xxl']}",
        f"{str(video_path)}",
        "--model", model,
        "--temperature", "0.0",
        "--model_dir", cfg['paths']['model_dir'],
        "--language", cfg['whisper']['args']['language'],
        "--word_timestamps", cfg['whisper']['args']['word_timestamps'],
        "--vad_filter", cfg['whisper']['args']['vad_filter'],
        "--vad_method", cfg['whisper']['args']['vad_method'],
        "--repetition_penalty", "1.5",
        "--no_repeat_ngram_size", "3",
        "--beam_size", "8",
        "--hotwords", 'cock, pussy, dick, sex, ass, nude, naked, porn, hard, cum, fuck, cumshot, cumming',
        "--output_format", cfg['whisper']['args']['output_format'],
        "--output_dir", output_dir,
        "--max_line_width", str(cfg['whisper']['args']['max_line_width']),
        "--max_line_count", str(cfg['whisper']['args']['max_line_count'])
    ]
    
    if prompt := cfg['whisper'].get('prompt'):
        command.extend(["--initial_prompt", prompt])
    
    return command

def run_whisper_process(command, stopped_flag=None):
    """运行 Whisper 进程并实时读取输出"""
    process = None # 初始化 process

    # --- 定义内部函数 --- (移动到前面)
    def read_output(pipe, prefix=""):
        """读取管道输出"""
        try:
            # 使用 iter(pipe.readline, '') 确保实时读取
            for line in iter(pipe.readline, ''):
                print(f"{prefix}: {line.strip()}")
        except Exception as e:
            # 捕捉读取过程中的异常，例如管道关闭
            # print(f"读取 {prefix} 时出错 (可能管道已关闭): {e}")
            pass # 忽略读取错误，例如管道关闭
        finally:
            # 确保管道关闭，即使发生错误
            if pipe and not pipe.closed:
                try:
                    pipe.close()
                except Exception as close_err:
                    print(f"关闭 {prefix} 管道时出错: {close_err}")
    # -------------------

    try:
        print(f"执行命令: {' '.join(command)}")
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1, universal_newlines=True)

        # 现在可以安全地使用 read_output
        stdout_thread = threading.Thread(target=read_output, args=(process.stdout, "STDOUT"), daemon=True)
        stderr_thread = threading.Thread(target=read_output, args=(process.stderr, "STDERR"), daemon=True)

        stdout_thread.start()
        stderr_thread.start()

        while process.poll() is None:
            # 在等待进程结束时检查停止标志
            if stopped_flag and stopped_flag():
                print("检测到停止请求，尝试终止 Whisper 进程...")
                try:
                    process.terminate()
                    # process.wait(timeout=5) # 等待一段时间让进程结束 (可选)
                except Exception as e:
                    print(f"终止 Whisper 进程时出错: {e}")
                # 返回 None 表示被中断
                stdout_thread.join(timeout=1)
                stderr_thread.join(timeout=1)
                return None
            time.sleep(0.5)  # 减少CPU占用

        # 确保线程结束，获取所有剩余输出
        stdout_thread.join(timeout=2)
        stderr_thread.join(timeout=2)

        # 检查最终返回码
        if process.returncode != 0:
            print(f"Whisper 进程错误退出，返回码: {process.returncode}")
            
            # 特殊处理栈缓冲区溢出错误 - 字幕可能已生成
            if process.returncode == 3221226505:
                print("检测到栈缓冲区溢出错误，但字幕可能已成功生成")
                return process  # 返回进程对象，让上层检查文件
            
            return None # 其他错误返回 None

        print("Whisper 进程成功完成")
        return process # 返回进程对象（虽然可能没用，但保留可能性）

    except FileNotFoundError:
        print(f"错误：无法找到 Whisper 执行文件或命令中的其他文件。请检查路径设置。命令: {' '.join(command)}")
        return None
    except Exception as e:
        print(f"运行 Whisper 进程时发生意外错误: {e}")
        if process and process.poll() is None:
            try:
                process.terminate()
                stdout_thread.join(timeout=1)
                stderr_thread.join(timeout=1)
            except Exception as term_err:
                print(f"终止 Whisper 进程时出错（异常中）: {term_err}")
        return None

def find_subtitle_in_directory(directory, base_name):
    """在指定目录中查找与基本名称匹配的字幕文件"""
    print(f"在目录 {directory} 中查找与 {base_name} 相关的字幕文件")
    
    # 列出目录中的所有文件
    if os.path.exists(directory):
        print("目录中的文件:")
        for file in os.listdir(directory):
            print(f" - {file}")
    
    # 检查直接匹配的文件
    direct_match = os.path.join(directory, f"{base_name}.srt")
    if os.path.exists(direct_match):
        print(f"找到直接匹配的字幕文件: {direct_match}")
        return direct_match
    
    # 检查常见的命名模式
    patterns = [
        f"{base_name}.srt",
        f"{base_name}.en.srt",
        f"{base_name}_en.srt",
        f"{base_name}.zh.srt",
        f"{base_name}_zh.srt",
        f"{base_name}.ja.srt",
        f"{base_name}_ja.srt",
        f"{base_name}.ko.srt",
        f"{base_name}_ko.srt",
    ]
    
    for pattern in patterns:
        path = os.path.join(directory, pattern)
        if os.path.exists(path):
            print(f"找到匹配的字幕文件: {path}")
            return path
    
    # 查找以基本名称开头的任何.srt文件
    for file in os.listdir(directory):
        if file.startswith(base_name) and file.endswith(".srt"):
            path = os.path.join(directory, file)
            print(f"找到以基本名称开头的字幕文件: {path}")
            return path
    
    # 查找目录中的任何.srt文件（最后的尝试）
    for file in os.listdir(directory):
        if file.endswith(".srt"):
            path = os.path.join(directory, file)
            print(f"找到目录中的字幕文件: {path}")
            return path
    
    print(f"在目录 {directory} 中未找到任何字幕文件")
    return None

def find_generated_subtitle(video_path):
    """查找生成的字幕文件"""
    return find_subtitle_in_directory(video_path.parent, video_path.stem)

def handle_whisper_error(error, output_path):
    """处理Whisper错误"""
    # 错误码 3221226505 (0xC0000409) 是栈缓冲区溢出，但字幕可能已生成
    if error.returncode == 3221226505:
        print(f"检测到Whisper栈缓冲区溢出错误 (3221226505)，但字幕可能已生成")
        if os.path.exists(output_path):
            print(f"找到生成的字幕文件: {output_path}")
            return output_path
        else:
            print(f"字幕文件不存在: {output_path}")
            return None
    else:
        print(f"字幕生成失败")
        print(f"错误信息: {error}")
        return None
        
if __name__ == "__main__":
    import sys
    
    # 获取输入文件路径
    def get_input_file():
        if len(sys.argv) >= 2:
            # 有命令行参数，使用第一个参数作为文件路径
            return sys.argv[1]
        else:
            # 没有命令行参数，提示用户输入
            return input('请输入需要生成字幕的视频文件路径: ').replace('"', '').strip()
    
    # 处理文件
    def process_video_file(file_path):
        print(f"开始处理视频文件: {file_path}")
        srt_path = generate_subtitle(file_path)
        if srt_path:
            print(f"字幕生成完成: {srt_path}")
        else:
            print("字幕生成失败")
        return srt_path
    
    # 主执行流程
    try:
        input_file = get_input_file()
        if input_file:
            process_video_file(input_file)
        else:
            print("未提供有效的文件路径")
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")



