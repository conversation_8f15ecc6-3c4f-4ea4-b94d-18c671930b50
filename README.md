# 视频字幕处理工具

## 项目概述

这是一个基于Python开发的综合性视频字幕处理系统，具有图形用户界面(GUI)。该工具可以自动从视频中提取音频内容并生成字幕文件，然后对字幕进行优化处理，最后使用本地大语言模型将字幕翻译成中文。

## 主要功能

1. **视频转写**
   - 支持整体处理模式和分段处理模式
   - 可配置不同模型处理视频的不同部分
   - 自动生成SRT格式字幕文件

2. **字幕优化**
   - 过滤无意义词语和短句
   - 合并短句提高可读性
   - 智能处理多行字幕

3. **字幕翻译**
   - 使用本地Ollama模型进行翻译
   - 支持批量翻译提高效率
   - 保留原文格式和时间戳

4. **其他功能**
   - 双语字幕拆分
   - 批量处理多个视频或字幕文件
   - 支持文件拖放操作
   - 完整的配置界面

## 系统要求

- 操作系统: Windows
- Python 3.8+
- GPU支持(推荐用于加速模型处理)
- 本地安装的Ollama服务(用于翻译)

## 使用说明

1. **启动应用**
   ```
   python gui.py
   ```

2. **选择处理模式**
   - 完整处理: 依次执行转写、优化和翻译
   - 仅转写: 只从视频中提取字幕
   - 仅优化: 只进行字幕优化
   - 重新翻译: 拆分双语字幕并重新翻译

3. **选择文件**
   - 点击"选择文件"按钮选择单个文件
   - 点击"选择文件夹"按钮选择整个文件夹
   - 直接拖放文件或文件夹到文件列表区域

4. **配置设置**
   - 点击"设置"按钮打开设置对话框
   - 配置路径、模型参数、翻译设置等

5. **开始处理**
   - 点击"开始处理"按钮开始处理文件
   - 处理过程中可以查看日志和进度
   - 可随时点击"停止处理"按钮终止操作

## 配置详情

配置文件(`config.yaml`)包含以下主要设置:

- **文件类型**
  ```yaml
  filetype:
    video_extensions: [.mp4, .mkv, .avi, .ts]
    subtitle_extensions: [.srt, .ass]
  ```

- **路径设置**
  ```yaml
  paths:
    whisper_xxl: 路径到whisper可执行文件
    model_dir: 模型存储目录
    output_dir: 输出目录
  ```

- **Whisper处理参数**
  ```yaml
  whisper:
    process_mode: segment  # 或 whole
    whole_model: large-v2  # 整体模式使用的模型
    first_segment_model: large-v2  # 首段使用的模型
    other_segments_model: large-v3  # 其他段使用的模型
    segment_length: 960  # 分段长度(秒)
  ```

- **翻译设置**
  ```yaml
  translation:
    model: qwen  # 翻译使用的模型
    batch_size: 5  # 每批处理的字幕条数
    max_workers: 4  # 并行处理线程数
  ```

- **优化设置**
  ```yaml
  optimize:
    min_sentence_length: 8  # 最小句子长度
    merge_interval: 1.5  # 合并间隔时间(秒)
    remove_meaningless: true  # 是否移除无意义词
  ```

## 注意事项

- **模型区分**：
  - **Whisper模型**：用于视频转录（语音转文字），存储在model_dir目录中
  - **Ollama模型**：用于字幕翻译，通过本地Ollama服务提供
- 确保本地Ollama服务正常运行，可通过命令`ollama list`查看可用翻译模型
- 确保Whisper模型已下载到配置的model_dir目录中
- 处理大型视频文件时，请确保有足够的磁盘空间和内存
- 翻译过程依赖网络连接，请确保网络稳定
- 首次使用时，Whisper模型可能需要下载，这可能需要一些时间

## 模型配置说明

### Whisper转录模型
- 模型存储位置：`paths.model_dir`配置的目录
- 模型命名：去掉"faster-"前缀，再去掉"whisper-"部分（如"faster-whisper-large-v2" → "large-v2"，"faster-distil-whisper-large-v3.5" → "distil-large-v3.5"）
- 用途：将视频音频转录为文字字幕

### Ollama翻译模型  
- 获取方式：通过`ollama list`命令查看已安装模型
- 安装新模型：`ollama pull 模型名`
- 用途：将英文字幕翻译为中文 