import yaml
from pathlib import Path
import os
import shutil
import traceback

def get_config_path(config_file="config.yaml"):
    """获取配置文件的绝对路径"""
    return os.path.abspath(config_file)

def load_config(config_file="config.yaml"):
    """加载YAML配置文件"""
    try:
        with open(config_file, encoding='utf-8') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        print(f"配置文件 {config_file} 不存在")
        return {}

def save_config(config_data, config_file="config.yaml"):
    """保存配置到YAML文件"""
    try:
        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(os.path.abspath(config_file)), exist_ok=True)
        
        # 创建临时文件
        temp_file = f"{config_file}.temp"
        
        # 直接写入到临时文件
        with open(temp_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True, sort_keys=False)
        
        # 如果配置文件已存在，尝试创建备份 - 这一步在SettingsDialog中已处理，这里不再重复
        # 直接将临时文件重命名为正式配置文件
        try:
            if os.path.exists(config_file):
                os.remove(config_file)  # 确保目标文件不存在
            os.rename(temp_file, config_file)
        except Exception as e:
            # 如果重命名失败，尝试直接复制
            print(f"重命名临时文件失败: {str(e)}，尝试直接复制...")
            shutil.copy2(temp_file, config_file)
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        print(f"配置已保存到: {config_file}")
        return True
    except Exception as e:
        print(f"保存配置时出错: {str(e)}")
        traceback.print_exc()
        return False

def update_config(updates, config_file="config.yaml"):
    """更新配置文件中的特定字段
    
    Args:
        updates (dict): 要更新的配置键值对
        config_file (str): 配置文件路径
    
    Returns:
        dict: 更新后的完整配置
    """
    # 加载当前配置
    current_config = load_config(config_file)
    
    # 递归更新配置
    def update_dict(d, u):
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                # 递归更新嵌套字典
                d[k] = update_dict(d[k], v)
            else:
                # 直接更新值
                d[k] = v
        return d
    
    # 更新配置
    updated_config = update_dict(current_config, updates)
    
    # 保存更新后的配置
    save_config(updated_config, config_file)
    
    return updated_config

def get_config_value(key_path, default=None, config_file="config.yaml"):
    """获取配置中的特定值
    
    Args:
        key_path (str): 点分隔的键路径，如 "translation.model"
        default: 如果键不存在时的默认值
        config_file (str): 配置文件路径
    
    Returns:
        配置值或默认值
    """
    config = load_config(config_file)
    keys = key_path.split('.')
    
    # 遍历键路径
    current = config
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    
    return current

def get_video_extensions():
    """获取配置中定义的视频文件扩展名"""
    config_data = load_config()
    return config_data.get('filetype', {}).get('video_extensions', [])

def get_subtitle_extensions():
    """获取配置中定义的字幕文件扩展名"""
    config_data = load_config()
    return config_data.get('filetype', {}).get('subtitle_extensions', [])
