import requests
import json

def stream_ollama(prompt, model="qwen2.5:14b", temperature=0.7, max_tokens=500):
    """
    流式输出版本，支持更多参数
    参数:
        prompt (str): 用户提示
        model (str): 模型名称
        temperature (float): 控制随机性 (0-1)
        max_tokens (int): 最大输出token数
    """
    url = "http://localhost:11434/api/generate"
    headers = {'Content-Type': 'application/json'}
    
    data = {
        "model": model,
        "prompt": prompt,
        "stream": True,
        "options": {
            "temperature": temperature,
            "max_tokens": max_tokens
        }
    }

    try:
        with requests.post(url, headers=headers, json=data, stream=True) as response:
            response.raise_for_status()
            
            full_response = []
            for line in response.iter_lines():
                if line:
                    chunk = json.loads(line.decode('utf-8'))
                    if not chunk["done"]:
                        print(chunk["response"], end="", flush=True)
                        full_response.append(chunk["response"])
            return "\n".join(full_response)
            
    except requests.exceptions.RequestException as e:
        print(f"\n请求错误: {str(e)}")
        return ""

# 使用示例
stream_ollama("用简单的语言解释量子计算", temperature=0.8)