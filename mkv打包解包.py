import os
import json
import subprocess
import shutil
from tqdm import tqdm

def get_mkv_info(file_path):
    """获取 MKV 文件的详细信息。"""
    try:
        command = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', '-show_streams', file_path]
        result = subprocess.run(command, capture_output=True, text=True, encoding='utf-8', errors='replace')
        if result.returncode != 0:
            print(f"ffprobe 命令执行失败。错误信息：{result.stderr}")
            return None
        return json.loads(result.stdout)
    except Exception as e:
        print(f"获取文件信息时发生错误：{e}")
        return None

def get_duration(file_info):
    """从文件信息中获取持续时间（秒）。"""
    try:
        return float(file_info['format']['duration'])
    except KeyError:
        return None

def run_ffmpeg_command(command, total_duration):
    """运行 ffmpeg 命令并显示进度条。"""
    process = subprocess.Popen(command, stderr=subprocess.PIPE, bufsize=1, universal_newlines=True, encoding='utf-8', errors='replace')
    
    pbar = tqdm(total=100, unit='%', desc="处理进度")
    
    for line in process.stderr:
        if "time=" in line:
            try:
                time_str = line.split("time=")[1].split()[0]
                hours, minutes, seconds = map(float, time_str.split(':'))
                current_time = hours * 3600 + minutes * 60 + seconds
                progress = min(int((current_time / total_duration) * 100), 100)
                pbar.update(progress - pbar.n)
            except Exception as e:
                print(f"处理进度信息时出错：{e}")
    
    pbar.close()
    process.wait()

def process_mkv(file_path):
    """根据 MKV 文件的内容，执行解包操作。"""
    if not file_path.endswith('.mkv'):
        print("输入文件不是 MKV 格式。")
        return

    file_info = get_mkv_info(file_path)
    if file_info is None:
        print("无法获取文件信息，退出处理。")
        return

    total_duration = get_duration(file_info)
    if total_duration is None:
        print("无法获取文件时长，进度显示可能不准确。")
        total_duration = 100  # 假设一个默认值

    output_dir = os.path.splitext(file_path)[0]
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    base_name = os.path.splitext(os.path.basename(file_path))[0]

    # 分别提取各类型流
    video_path = os.path.join(output_dir, f"{base_name}_video.mp4")
    audio_path = os.path.join(output_dir, f"{base_name}_audio.m4a")
    subtitle_path = os.path.join(output_dir, f"{base_name}_subtitle.srt")
    
    # 动态生成各流参数
    command = ['ffmpeg', '-i', file_path]
    
    # 视频流处理
    command += ['-map', '0:v', '-c:v', 'copy', video_path]
    
    # 音频流处理（带元数据）
    audio_streams = [s for s in file_info['streams'] if s['codec_type'] == 'audio']
    print("\n检测到音频流：")
    for idx, stream in enumerate(audio_streams):
        lang = stream.get('tags', {}).get('language', 'unk').upper()
        title = stream.get('tags', {}).get('title', '').replace(' ', '_')
        safe_title = "".join([c for c in title if c.isalnum() or c in ('_', '-')]) or f"track{idx}"
        print(f"  音轨{idx}: 语言={lang} 标题={title}")
        out_name = f"{base_name}_audio_{lang}_{safe_title}.m4a"
        command += ['-map', f'0:a:{idx}', '-c:a', 'copy', 
                   os.path.join(output_dir, out_name)]
    
    # 字幕流处理（带元数据）
    sub_streams = [s for s in file_info['streams'] if s['codec_type'] == 'subtitle']
    print("\n检测到字幕流：")
    for idx, stream in enumerate(sub_streams):
        lang = stream.get('tags', {}).get('language', 'unk').upper()
        title = stream.get('tags', {}).get('title', '').replace(' ', '_')
        safe_title = "".join([c for c in title if c.isalnum() or c in ('_', '-')]) or f"track{idx}"
        print(f"  字幕{idx}: 语言={lang} 标题={title}")
        out_name = f"{base_name}_subtitle_{lang}_{safe_title}.srt"
        command += ['-map', f'0:s:{idx}', '-c:s', 'copy', 
                   os.path.join(output_dir, out_name)]
    
    command += ['-max_muxing_queue_size', '1024']

    print(f"开始提取视频流到：{video_path}")
    print(f"开始提取音频流到：{audio_path}") 
    print(f"开始提取字幕流到：{subtitle_path}")
    run_ffmpeg_command(command, total_duration)

    print(f"已将 MKV 文件解包到目录：{output_dir}")

def create_mkv(video_path):
    """
    将视频文件和字幕打包成 MKV 文件。

    参数：
        video_path (str): 视频文件路径。

    返回：
        None
    """

    # 确保 video_path 是绝对路径
    video_path = os.path.abspath(video_path)

    # 获取原视频文件目录
    video_dir = os.path.dirname(video_path)
    print(video_dir)

    subtitle_path = f"{os.path.splitext(video_path)[0]}.srt"

    # 检查字幕文件是否存在
    if not os.path.exists(subtitle_path):
        print(f"字幕文件 '{subtitle_path}' 不存在。")
        return

    # 生成 MKV 文件的名称和路径
    mkv_name = f"{os.path.splitext(os.path.basename(video_path))[0]}.mkv"

    # 使用 ffmpeg 命令打包视频和字幕
    command = f'ffmpeg -i "{video_path}" -i "{subtitle_path}" -c:v copy -c:a copy -c:s copy "{os.path.join(video_dir, mkv_name)}"'
    os.system(command)

    print(f"已将视频和字幕打包成 MKV 文件：{os.path.join(video_dir, mkv_name)}")

def main():
    """
    主函数，从用户输入获取文件路径并执行操作。
    """

    while True:
        # 获取用户输入的文件路径
        文件_路径 = input("请输入文件路径 (或 'quit' 退出): ").replace('"', '')

        # 判断用户是否输入 'quit' 退出
        if 文件_路径.lower() == "quit":
            break

        # 检查文件是否存在
        if not os.path.exists(文件_路径):
            print(f"错误：文件 '{文件_路径}' 不存在。")
            continue

        # 判断文件类型并执行相应操作
        if 文件_路径.endswith('.mkv'):
            # 解包 MKV 文件
            process_mkv(文件_路径)
            output_dir=os.path.splitext(文件_路径)[0]
            print(f"已将 MKV 文件解包到目录：{output_dir}")
        else:
            # 打包视频文件和字幕
            create_mkv(文件_路径)


if __name__ == "__main__":
    main()
