# 字幕处理工具编码规则和最佳实践

## 代码结构规则
- 使用一致的缩进（4个空格）
- 各功能模块通过明确的接口交互，避免直接调用内部方法
- 避免全局变量，优先使用类属性或函数参数传递数据
- 模块化设计：GUI、翻译引擎、字幕处理、设置管理应各自独立

## 线程安全规则
- UI更新必须在主线程执行，使用after(0, callback)方法调度
- 长时间操作应放在后台线程中执行
- 使用队列或锁机制确保线程安全
- 翻译和处理进度必须安全地传递到UI线程

## 错误处理规则
- 每个可能失败的操作都应有明确的错误处理机制
- 提供用户友好的错误消息，同时记录详细的技术信息
- 避免吞掉异常，确保错误可以被跟踪和诊断
- 在UI中展示错误信息前，确保在主线程执行

## UI交互规则
- 提供明确的用户反馈，特别是长时间操作
- 确保UI元素查找使用稳健的方法，避免依赖布局信息
- 所有长时间运行的操作都应提供取消选项
- 定期更新进度条和状态信息，避免界面看起来冻结

## 配置管理规则
- 配置变更应通过专门的接口进行，避免直接修改配置对象
- 配置保存前应进行验证
- 提供配置备份和恢复机制
- 安全地处理文件操作，避免覆盖或损坏用户数据

## 翻译引擎规则
- 翻译进度必须正确传递到GUI
- 使用统一的回调接口更新进度
- 避免在翻译类中直接引用GUI组件
- 提供详细的翻译状态日志

## 设置对话框规则
- 与主界面逻辑解耦
- 使用模型引用而非布局信息查找组件
- 刷新模型列表应在后台线程执行
- 取消按钮应撤销所有未保存的更改

## 代码样式规则
- 函数和变量命名应遵循一致的规范（如蛇形命名法）
- 每个类和函数应有文档字符串，说明其用途和参数
- 避免过长的函数和过度复杂的条件语句
- 遵循PEP 8编码风格指南

## 测试建议
- 为关键功能编写单元测试，特别是容易出错的部分
- 测试边界条件和错误处理路径
- 使用模拟对象测试依赖外部服务的代码
- 确保GUI和后端逻辑可分别测试

## 常见问题防范
- 修改一个模块前，考虑对其他模块的影响
- 执行外部命令前检查命令是否可用
- 任何文件操作前检查路径是否存在和权限
- 确保所有UI更新在主线程执行，避免线程安全问题
- 控制UI更新频率，避免过度频繁导致界面卡顿 