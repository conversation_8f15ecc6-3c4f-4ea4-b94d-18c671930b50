# 字幕质量优化功能说明

## 功能概述

字幕质量优化功能是一个自动化的后处理系统，专门用于改善长时间字幕片段的质量。该功能会自动检测持续时间过长的字幕条目，并对这些片段进行重新转写，以提高字幕的准确性和可读性。

## 工作原理

### 1. 问题识别
- 自动扫描生成的SRT字幕文件
- 识别持续时间超过设定阈值（默认10秒）的字幕条目
- 这些长时间片段通常表示：
  - 音频识别不准确
  - 内容过于复杂或冗长
  - 需要更精细的时间分割

### 2. 音频提取
- 根据原始字幕的时间戳从源视频中提取对应的音频片段
- 使用FFmpeg进行精确的音频片段提取
- 转换为适合Whisper处理的音频格式（16kHz, 单声道, PCM）

### 3. 重新转写
- 使用指定的Whisper模型对提取的音频片段进行重新转写
- 可以选择使用更高精度的模型进行优化
- 支持与原始转写相同的语言和参数设置

### 4. 时间戳对齐
- 将重新转写的结果调整到原始时间范围内
- 确保新生成的字幕片段不与相邻字幕重叠
- 保持时间轴的连续性和准确性

### 5. 结果合并
- 将优化后的字幕片段替换原SRT文件中对应的长时间条目
- 重新排序和编号所有字幕条目
- 创建原始文件的备份

## 配置选项

### 在config.yaml中的配置

```yaml
subtitle_quality:
  enable_optimization: true          # 是否启用字幕质量优化
  duration_threshold: 10.0           # 时长阈值（秒），超过此时长的字幕将被优化
  optimization_model: large-v3-turbo-ct2  # 用于重新转写的模型
  max_segments_to_optimize: 5        # 单次处理的最大优化片段数
```

### 在GUI界面中的设置

1. 打开设置对话框
2. 切换到"优化设置"选项卡
3. 在"字幕质量优化设置"部分配置：
   - **启用质量优化**: 开启/关闭此功能
   - **时长阈值(秒)**: 设置触发优化的最小时长
   - **优化模型**: 选择用于重新转写的Whisper模型
   - **最大优化片段数**: 限制单次处理的片段数量

## 使用方法

### 自动触发
字幕质量优化功能会在字幕生成完成后自动运行，无需手动干预。

### 处理流程
1. 正常生成字幕（整段模式或分段模式）
2. 自动检测长时间片段
3. 如果找到需要优化的片段，开始优化过程
4. 显示优化进度和结果
5. 保存优化后的字幕文件

### 输出信息
```
开始字幕质量优化...
找到 2 个需要优化的长时间片段（阈值: 10.0秒）
优化片段 1/2: 00:01:30,000 --> 00:01:45,000 (时长: 15.0秒)
使用模型 large-v3-turbo-ct2 重新转写片段 1
片段 1 优化完成
优化片段 2/2: 00:03:20,000 --> 00:03:35,000 (时长: 15.0秒)
使用模型 large-v3-turbo-ct2 重新转写片段 2
片段 2 优化完成
成功优化了 2 个片段
合并优化后的字幕片段...
原始字幕已备份到: video.srt.backup
字幕质量优化完成，已保存到: video.srt
优化统计: 处理了 2 个长片段，成功优化 2 个
```

## 技术细节

### 时间戳处理算法
1. 计算原始长片段的时长
2. 获取重新转写结果的总时长
3. 计算时间缩放比例
4. 按比例调整每个新字幕片段的时间戳
5. 确保所有时间戳都在原始时间范围内

### 音频提取参数
- 采样率: 16kHz
- 声道: 单声道
- 编码: PCM 16位
- 格式: WAV

### 错误处理
- 音频提取失败时保留原始字幕
- 重新转写失败时保留原始字幕
- 发生错误时自动恢复原始文件
- 详细的错误日志记录

## 性能考虑

### 处理时间
- 优化时间取决于需要处理的片段数量和长度
- 每个片段的处理时间约为其音频时长的1-3倍
- 建议在处理长视频时适当调整最大优化片段数

### 资源使用
- 需要额外的磁盘空间存储临时音频文件
- 重新转写过程会使用GPU/CPU资源
- 临时文件会在处理完成后自动清理

## 最佳实践

### 模型选择
- 对于高质量要求：使用 `large-v3` 或 `large-v3-turbo-ct2`
- 对于速度优先：使用 `medium` 或 `small`
- 建议优化模型不低于原始转写模型

### 阈值设置
- 默认10秒适合大多数场景
- 对话密集的内容可以降低到8秒
- 演讲类内容可以提高到15秒

### 片段数量限制
- 默认5个片段平衡了质量和处理时间
- 短视频可以增加到10个
- 长视频建议保持在5个以内

## 故障排除

### 常见问题

1. **优化功能未启动**
   - 检查配置中的 `enable_optimization` 是否为 `true`
   - 确认找到了超过阈值的长时间片段

2. **音频提取失败**
   - 确认FFmpeg已正确安装并在PATH中
   - 检查视频文件是否损坏
   - 确认有足够的磁盘空间

3. **重新转写失败**
   - 检查Whisper模型是否正确安装
   - 确认模型路径配置正确
   - 检查系统资源是否充足

4. **时间戳不准确**
   - 这通常是由于音频内容与原始识别差异较大
   - 可以尝试使用更高精度的模型
   - 检查原始字幕的时间戳是否准确

### 日志分析
查看控制台输出中的详细信息：
- 优化过程的每个步骤都有日志记录
- 错误信息会显示具体的失败原因
- 成功统计会显示优化效果

## 版本历史

- v1.0: 初始版本，支持基本的长片段检测和重新转写
- 未来计划: 支持更智能的片段分割算法，增加质量评估指标
