import pysrt
from call_ollama import generate_with_ollama
import time
import os
from utill import is_bilingual_subtitle, is_chinese, is_english, is_chinese_text, split_sub

# 配置参数
SEGMENT_SIZE = 20       # 每段字幕数量 - 较大值可减少段数但增加翻译难度
OVERLAP_SIZE = 3        # 段间重叠字幕数量 - 用于保持上下文连贯性
MAX_RETRIES = 3         # 最大重试次数 - 翻译长度不匹配时的重试次数

sys_prompt=r"你是一个字幕翻译助手。能把字幕中的英语对话准确的翻译为中文对话。在能充分理解原对话中前后文的情况下，能用合适的符合中文表达习惯的语言准确的翻译出来，只翻译文字部分，不翻译人名，序号和时间码保持原样不翻译。"

# 使用utill.py中现有的函数，不重复定义

def extract_english_from_bilingual(srt_path: str) -> str:
    """从双语字幕中提取英文部分，使用utill.py中的split_sub函数"""
    print("正在从双语字幕中拆分中英文...")
    original_path, zh_path = split_sub(srt_path)
    
    if original_path and os.path.exists(original_path):
        print(f"英文字幕已提取到: {original_path}")
        return original_path
    else:
        print("拆分失败，无法提取英文字幕")
        return None

def chunk_list_overlap(lst, n, overlap=0):
    step = n - overlap
    if overlap >= n:
        raise ValueError("Overlap can't be greater than or equal to chunk size.")
    return [lst[i:i + n] for i in range(0, len(lst), step)]

def srt2str(lst):
    return '\n'.join([str(n) for n in lst])

def smart_match_subtitles(en_subs, zh_subs):
    """
    智能匹配中英文字幕，基于时间码和序号进行对齐
    :param en_subs: 英文字幕列表
    :param zh_subs: 中文字幕列表
    :return: 匹配后的字幕对列表
    """
    matched_pairs = []
    zh_used = set()  # 记录已使用的中文字幕索引
    
    for en_sub in en_subs:
        best_match = None
        best_score = -1
        best_zh_idx = -1
        
        for zh_idx, zh_sub in enumerate(zh_subs):
            if zh_idx in zh_used:
                continue
            
            # 计算匹配分数
            score = 0
            
            # 1. 序号匹配（权重40%）
            if hasattr(en_sub, 'index') and hasattr(zh_sub, 'index'):
                if en_sub.index == zh_sub.index:
                    score += 0.4
            
            # 2. 时间码匹配（权重60%）
            if hasattr(en_sub, 'start') and hasattr(zh_sub, 'start'):
                # 计算开始时间差（秒）
                en_start = en_sub.start.hours * 3600 + en_sub.start.minutes * 60 + en_sub.start.seconds + en_sub.start.milliseconds / 1000.0
                zh_start = zh_sub.start.hours * 3600 + zh_sub.start.minutes * 60 + zh_sub.start.seconds + zh_sub.start.milliseconds / 1000.0
                time_diff = abs(en_start - zh_start)
                
                # 时间差越小，分数越高（最大5秒容忍度）
                if time_diff <= 5:
                    score += 0.6 * (1 - time_diff / 5)
            
            # 更新最佳匹配
            if score > best_score:
                best_score = score
                best_match = zh_sub
                best_zh_idx = zh_idx
        
        # 如果找到合理匹配（分数>0.3），添加到结果
        if best_match and best_score > 0.3:
            matched_pairs.append((en_sub, best_match))
            zh_used.add(best_zh_idx)
        else:
            # 没有找到匹配的中文字幕，使用原英文
            matched_pairs.append((en_sub, None))
    
    return matched_pairs

def merged_srt_with_smart_match(en_subs, zh_subs):
    """
    使用智能匹配合并中英文字幕
    :param en_subs: 英文字幕对象组
    :param zh_subs: 中文字幕对象组  
    :return: 合并后的双语字幕对象组
    """
    # 处理输入参数
    if hasattr(en_subs, '__iter__') and not isinstance(en_subs, str):
        en_list = list(en_subs)
    else:
        raise ValueError("英文字幕参数必须是可迭代的字幕对象")
    
    if hasattr(zh_subs, '__iter__') and not isinstance(zh_subs, str):
        zh_list = list(zh_subs)
    else:
        raise ValueError("中文字幕参数必须是可迭代的字幕对象")
    
    # 检查长度是否一致
    if len(en_list) != len(zh_list):
        print(f"检测到中英文字幕长度不一致 (英文: {len(en_list)}, 中文: {len(zh_list)})")
        print("启用智能匹配模式...")
        matched_pairs = smart_match_subtitles(en_list, zh_list)
    else:
        # 长度一致，直接配对
        matched_pairs = [(en, zh) for en, zh in zip(en_list, zh_list)]
    
    # 创建合并后的字幕列表
    merged_subs = []
    
    for i, (en_sub, zh_sub) in enumerate(matched_pairs):
        try:
            # 使用英文字幕的时间信息
            start_time = en_sub.start
            end_time = en_sub.end
            
            # 获取文本内容
            en_text = en_sub.text.strip() if hasattr(en_sub, 'text') and en_sub.text else ""
            zh_text = zh_sub.text.strip() if zh_sub and hasattr(zh_sub, 'text') and zh_sub.text else ""
            
            # 注意：这里不再重复清理，因为在translate_with_retry中已经清理过了
            
            # 跳过空字幕
            if not en_text:
                continue
            
            # 检查中文翻译是否已经包含英文（避免重复）
            if zh_text and en_text.lower() in zh_text.lower():
                # 如果中文翻译已包含英文，只使用中文翻译
                merged_text = zh_text
            elif zh_text:
                # 正常情况：英文+中文
                merged_text = f"{en_text}\n{zh_text}"
            else:
                # 只有英文
                merged_text = en_text
            
            # 创建新的字幕项
            new_sub = pysrt.SubRipItem(
                index=len(merged_subs) + 1,
                start=start_time,
                end=end_time,
                text=merged_text
            )
            merged_subs.append(new_sub)
            
        except Exception as e:
            print(f"处理第 {i+1} 条字幕时出错: {str(e)}")
            continue
    
    # 创建字幕文件对象
    try:
        merged_file = pysrt.SubRipFile(items=merged_subs)
        print(f"成功合并 {len(merged_subs)} 条双语字幕")
        return merged_file
    except Exception as e:
        print(f"创建字幕文件对象时出错: {str(e)}")
        return None

def save_merged_subtitle(merged_subs, output_path):
    """
    保存合并后的字幕到文件
    :param merged_subs: 合并后的字幕对象组
    :param output_path: 输出文件路径
    :return: 是否保存成功
    """
    try:
        if merged_subs is None:
            print("字幕对象为空，无法保存")
            return False
        
        merged_subs.save(output_path, encoding='utf-8')
        print(f"双语字幕已保存到: {output_path}")
        return True
    except Exception as e:
        print(f"保存字幕文件时出错: {str(e)}")
        return False

def clean_translation_text(text):
    """
    清理翻译文本，移除思考标签和其他垃圾信息（保守清理，确保不破坏SRT格式）
    :param text: 原始翻译文本
    :return: 清理后的文本
    """
    import re
    
    # 只做最基本的清理，避免破坏SRT格式
    
    # 移除 <think> 标签及其内容（不区分大小写）
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL | re.IGNORECASE)
    text = re.sub(r'<思考>.*?</思考>', '', text, flags=re.DOTALL)
    
    # 移除单独的 <think> 或 </think> 标签
    text = re.sub(r'</?think>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</?思考>', '', text)
    
    # 移除常见的翻译前缀，但只在行首
    text = re.sub(r'^(翻译结果|翻译|译文|中文翻译)[:：]\s*', '', text, flags=re.MULTILINE)
    
    # 清理过多的空行，但保持基本SRT格式
    text = re.sub(r'\n{3,}', '\n\n', text)
    
    return text.strip()

def translate_with_retry(en_srt_seg, max_retries=MAX_RETRIES):
    """
    带重试机制的翻译函数
    :param en_srt_seg: 英文字幕段
    :param max_retries: 最大重试次数
    :return: 中文字幕段对象，如果失败返回None
    """
    srt_seg = srt2str(en_srt_seg)
    
    for attempt in range(max_retries):
        try:
            print(f"翻译尝试 {attempt + 1}/{max_retries}...")
            translate_srt_seg = generate_with_ollama(system_prompt=sys_prompt, user_input=srt_seg)
            
            # 显示原始翻译结果（用于调试）
            print(f"原始翻译结果长度: {len(translate_srt_seg)} 字符")
            
            # 检查是否包含思考标签
            if '<think>' in translate_srt_seg.lower() or '<思考>' in translate_srt_seg or '思考' in translate_srt_seg:
                print("检测到思考标签，进行清理...")
            
            # 清理翻译结果
            cleaned_text = clean_translation_text(translate_srt_seg)
            print(f"清理后翻译结果长度: {len(cleaned_text)} 字符")
            
            # 如果清理后文本为空，使用原始文本
            if not cleaned_text.strip():
                print("警告：清理后文本为空，使用原始翻译结果")
                cleaned_text = translate_srt_seg
            
            zh_srt_seg = pysrt.from_string(cleaned_text)
            
            # 检查长度是否匹配
            if len(zh_srt_seg) == len(en_srt_seg):
                print(f"翻译成功，长度匹配: {len(zh_srt_seg)} 条字幕")
                return zh_srt_seg
            else:
                print(f"长度不匹配 (原文: {len(en_srt_seg)}, 译文: {len(zh_srt_seg)})，第 {attempt + 1} 次重试...")
                if len(zh_srt_seg) == 0:
                    print("译文长度为0，可能是清理过度，显示前200个字符：")
                    print(f"原始: {translate_srt_seg[:200]}...")
                    print(f"清理后: {cleaned_text[:200]}...")
                if attempt < max_retries - 1:
                    continue
                else:
                    print("达到最大重试次数，将使用智能匹配处理...")
                    return zh_srt_seg
                    
        except Exception as e:
            print(f"翻译失败 (尝试 {attempt + 1}): {str(e)}")
            if attempt < max_retries - 1:
                print("准备重试...")
                continue
            else:
                print("所有重试都失败，跳过此段...")
                return None
    
    return None

def process_subtitle_translation():
    """
    主翻译处理函数 - 使用统一的翻译函数，复用逻辑
    """
    # 获取输入文件路径
    def get_input_file():
        import sys
        if len(sys.argv) >= 2:
            return sys.argv[1]
        else:
            return input('请输入字幕路径: ').replace('"', '').strip()
    
    input_srt = get_input_file()
    if not input_srt:
        print("未提供有效的文件路径")
        return
    
    # 双语字幕处理逻辑
    if is_bilingual_subtitle(input_srt):
        print(f"检测到双语字幕文件: {input_srt}")
        choice = input("发现双语字幕，请选择处理方式：\n1. 提取英文部分重新翻译\n2. 退出程序\n请输入选项 (1/2): ").strip()
        
        if choice != "1":
            print("程序退出")
            return
    
    # 调用统一的翻译函数
    result = translate_subtitle_file(
        file_path=input_srt,
        segment_size=SEGMENT_SIZE,
        overlap_size=OVERLAP_SIZE,
        max_retries=MAX_RETRIES,
        progress_callback=None,
        stopped_flag=None,
        save_to_original=False  # 生成新文件
    )
    
    if result:
        print(f"\n✅ 翻译成功完成！")
        print(f"📁 输出文件: {result}")
    else:
        print("\n❌ 翻译失败")
        print("请检查输入文件和网络连接")

def translate_subtitle_file(file_path: str, segment_size=SEGMENT_SIZE, overlap_size=OVERLAP_SIZE, 
                          max_retries=MAX_RETRIES, progress_callback=None, stopped_flag=None, 
                          save_to_original=False):
    """
    统一的字幕翻译函数 - 复用现有逻辑，避免重复代码
    :param file_path: 字幕文件路径
    :param segment_size: 每段字幕数量
    :param overlap_size: 段间重叠数量
    :param max_retries: 最大重试次数
    :param progress_callback: 进度回调函数
    :param stopped_flag: 停止标志回调函数
    :param save_to_original: 是否覆盖原文件（True）或生成新文件（False）
    :return: 输出文件路径，失败返回None
    """
    file_path = str(file_path)
    
    # 检查停止标志
    if stopped_flag and stopped_flag():
        print("分段翻译操作已停止（开始时）")
        return None
    
    # 检查是否为双语字幕
    temp_english_file = None
    if is_bilingual_subtitle(file_path):
        print(f"检测到双语字幕文件: {file_path}")
        temp_english_file = extract_english_from_bilingual(file_path)
        if temp_english_file:
            actual_input_srt = temp_english_file
        else:
            print("提取英文字幕失败")
            return None
    else:
        print(f"检测到单语字幕文件: {file_path}")
        actual_input_srt = file_path
    
    # 检查停止标志
    if stopped_flag and stopped_flag():
        return None
    
    try:
        srt_obj = pysrt.open(actual_input_srt)
        print(f"成功加载字幕文件: {actual_input_srt}")
        print(f"共 {len(srt_obj)} 条字幕")
        print(f"配置参数: 每段{segment_size}条字幕, 重叠{overlap_size}条, 最大重试{max_retries}次")
    except Exception as e:
        print(f"无法打开字幕文件: {str(e)}")
        return None
    
    # 生成输出文件路径
    if save_to_original:
        output_path = file_path  # 直接覆盖原文件
    else:
        base_name = os.path.splitext(file_path)[0]
        output_path = f"{base_name}_bilingual.srt"
    
    # 执行分段翻译核心逻辑
    final_bilingual_subs = {}
    translated_indices = set()
    
    segments = list(chunk_list_overlap(srt_obj, segment_size, overlap_size))
    total_segments = len(segments)
    
    print(f"开始分段翻译，共 {total_segments} 段...")
    
    for current_segment, en_srt_seg in enumerate(segments, 1):
        # 检查停止标志
        if stopped_flag and stopped_flag():
            print(f"分段翻译在第 {current_segment} 段被停止")
            return None
        
        print(f"\n处理第 {current_segment}/{total_segments} 段 ({len(en_srt_seg)} 条字幕)...")
        print(f"本段字幕索引范围: {en_srt_seg[0].index} - {en_srt_seg[-1].index}")
        
        # 更新进度
        if progress_callback:
            progress = (current_segment - 1) / total_segments * 100
            progress_callback.update(progress, f"正在翻译第 {current_segment}/{total_segments} 段")
        
        # 过滤出未翻译的字幕
        untranslated_subs = [sub for sub in en_srt_seg if sub.index not in translated_indices]
        
        if not untranslated_subs:
            print("本段所有字幕已翻译过，跳过...")
            continue
        
        print(f"本段需翻译 {len(untranslated_subs)} 条字幕（跳过 {len(en_srt_seg) - len(untranslated_subs)} 条已翻译）")
        
        # 检查停止标志
        if stopped_flag and stopped_flag():
            return None
        
        # 带重试的翻译
        zh_srt_seg = translate_with_retry(untranslated_subs, max_retries)
        
        if zh_srt_seg is None:
            print("翻译失败，使用原英文字幕...")
            zh_srt_seg = []
        
        # 合并字幕
        try:
            dub_srt = merged_srt_with_smart_match(untranslated_subs, zh_srt_seg)
            if dub_srt:
                new_count = 0
                for i, bilingual_sub in enumerate(dub_srt):
                    if i < len(untranslated_subs):
                        original_index = untranslated_subs[i].index
                        final_bilingual_subs[original_index] = bilingual_sub
                        translated_indices.add(original_index)
                        new_count += 1
                
                print(f"本段新增 {new_count} 条双语字幕（总计: {len(final_bilingual_subs)}）")
            else:
                print("合并失败，跳过此段...")
        except Exception as e:
            print(f"合并字幕时出错: {str(e)}")
            continue
    
    # 检查停止标志
    if stopped_flag and stopped_flag():
        return None
    
    # 保存最终结果
    if final_bilingual_subs:
        print(f"\n处理完成，共生成 {len(final_bilingual_subs)} 条双语字幕")
        
        # 按索引排序并重新编号
        sorted_indices = sorted(final_bilingual_subs.keys())
        final_subs_list = []
        
        for new_index, orig_index in enumerate(sorted_indices, 1):
            sub = final_bilingual_subs[orig_index]
            sub.index = new_index
            final_subs_list.append(sub)
        
        # 创建最终字幕文件
        final_srt = pysrt.SubRipFile(items=final_subs_list)
        
        # 保存文件
        try:
            success = save_merged_subtitle(final_srt, output_path)
            if success:
                print(f"双语字幕已成功保存到: {output_path}")
                print(f"原始字幕: {len(srt_obj)} 条 → 双语字幕: {len(final_subs_list)} 条")
                
                # 更新最终进度
                if progress_callback:
                    progress_callback.update(100, f"翻译完成: 共处理{len(final_subs_list)}条字幕")
                
                # 清理临时文件
                if temp_english_file and os.path.exists(temp_english_file):
                    os.remove(temp_english_file)
                    print(f"已清理临时文件: {temp_english_file}")
                
                return output_path
            else:
                print("保存失败")
                return None
        except Exception as e:
            print(f"保存文件时出错: {str(e)}")
            return None
    else:
        print("没有生成任何双语字幕")
        return None

class SegmentTranslator:
    """分段翻译器类 - 轻量级封装，复用现有函数避免重复代码"""
    
    def __init__(self, segment_size=20, overlap_size=3, max_retries=3, progress_callback=None):
        self.segment_size = segment_size
        self.overlap_size = overlap_size
        self.max_retries = max_retries
        self.progress_callback = progress_callback
    
    def process_file(self, file_path: str, stopped_flag=None):
        """处理字幕文件 - 主要接口函数"""
        return translate_subtitle_file(
            file_path=file_path,
            segment_size=self.segment_size,
            overlap_size=self.overlap_size,
            max_retries=self.max_retries,
            progress_callback=self.progress_callback,
            stopped_flag=stopped_flag,
            save_to_original=True  # 覆盖原文件
        )
    
    def retranslate_subtitle(self, file_path: str, stopped_flag=None):
        """重新翻译字幕文件（兼容接口）"""
        print("使用分段翻译模式重新翻译字幕...")
        return self.process_file(file_path, stopped_flag)

if __name__ == "__main__":
    start_time = time.time()
    import sys
    
    # 主执行流程
    try:
        process_subtitle_translation()
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
    end_time = time.time()
    pass_time = end_time - start_time
    print(f'翻译用时：{pass_time:.2f}秒')
