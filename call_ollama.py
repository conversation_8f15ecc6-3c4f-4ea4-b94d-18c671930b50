import requests
import json
import subprocess
import re
import time
import traceback

def generate_with_ollama_cli(system_prompt=None, user_input=None, prompt=None, model="qwen2.5:latest"):
    """使用命令行方式调用Ollama，作为API失败时的备用方法"""
    # 兼容旧接口
    if prompt is not None and user_input is None:
        user_input = prompt
    
    if user_input is None:
        raise ValueError("必须提供user_input或prompt参数")
        
    # 确保system_prompt不为None
    if system_prompt is None:
        system_prompt = "你是一个有帮助的助手。"
    
    try:
        # 组合提示词
        combined_prompt = f"{system_prompt}\n\n{user_input}"
        
        # 对提示词进行转义，避免命令行错误
        escaped_prompt = combined_prompt.replace('"', '\\"').replace('$', '\\$').replace('`', '\\`')
        
        # 构建命令并执行
        cmd = f'ollama run {model} "{escaped_prompt}"'
        print(f"执行命令: {cmd[:100]}..." if len(cmd) > 100 else f"执行命令: {cmd}")
        
        result = subprocess.run(
            cmd, 
            shell=True,
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            timeout=120  # 设置超时时间为2分钟
        )
        
        if result.returncode == 0 and result.stdout:
            # 清理输出，去除<think>...</think>部分
            response = result.stdout
            response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL).strip()
            # 清理开头可能的提示重复
            prompt_prefix = user_input.split("\n")[0][:30]  # 取提示的前30个字符作为可能的前缀
            if prompt_prefix and response.startswith(prompt_prefix):
                response = response[len(prompt_prefix):].lstrip()
            print(f"命令行调用成功，响应长度: {len(response)} 字符")
            return response
        else:
            print(f"命令行调用失败，错误码: {result.returncode}")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return ""
    except Exception as e:
        print(f"命令行调用异常: {str(e)}")
        traceback.print_exc()
        return ""

def generate_with_ollama(
    system_prompt: str = None,
    user_input: str = None,
    prompt: str = None,  # 兼容旧版接口
    model: str = "qwen3:latest",
    max_tokens: int = None,
    temperature: float = 1,
    stream: bool = False, # 流式控制开关
    keep_alive: str = "15s" # 新增 keep_alive 参数，默认5秒
) -> str:
    """
    改进版聊天函数，支持流式输出控制和 keep_alive 设置

    参数:
        system_prompt (str): 系统级指令提示
        user_input (str): 用户输入内容
        prompt (str): 兼容旧版的prompt参数（如果提供，将作为user_input使用）
        model (str): 使用的模型名称
        max_tokens (int): 最大输出token数限制
        stream (bool): 是否启用流式输出，默认为False
        keep_alive (str): 控制模型在内存中保留的时间, e.g., "5m", "0s", "-1" (永久). 默认 "5m".
    """
    # 兼容旧接口
    if prompt is not None and user_input is None:
        user_input = prompt
    
    if user_input is None:
        raise ValueError("必须提供user_input或prompt参数")
        
    # 确保system_prompt不为None
    if system_prompt is None:
        system_prompt = "你是一个有帮助的助手。"
    
    url = "http://localhost:11434/api/chat"
    headers = {'Content-Type': 'application/json'}
    
    # 构造请求负载
    payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_input}
        ],
        "stream": stream,
        "keep_alive": keep_alive,
        "options": {
            "temperature": temperature
        }
    }
    
    # 添加token限制（如果提供）
    if max_tokens is not None:
        payload["options"]["max_tokens"] = max_tokens

    try:
        # print(f"向 Ollama 发送请求 (模型: {model}, keep_alive: {keep_alive})...")
        response = requests.post(url, headers=headers, json=payload, stream=stream)
        response.raise_for_status()
        
        full_response = []
        
        if stream:
            # 流式处理模式
            for line in response.iter_lines():
                if line:
                    try:
                        chunk = json.loads(line)
                        if 'message' in chunk:
                            content = chunk['message']['content']
                            print(content, end='', flush=True)
                            full_response.append(content)
                    except json.JSONDecodeError:
                        print(f"\n无法解析的流数据块: {line}")
        else:
            # 非流式处理模式
            result = response.json()
            if 'message' in result and 'content' in result['message']:
                full_response = [result['message']['content']]
            else:
                print(f"非流式响应格式错误: {result}")
                raise KeyError("响应中缺少 'message' 或 'content' 字段")
        
        final_text = ''.join(full_response).strip()
        # print(f"Ollama 响应接收完毕 (长度: {len(final_text)} 字符)")
        print(final_text)
        return final_text
        
    except requests.exceptions.JSONDecodeError as e:
        print(f"\nJSON解析失败: {str(e)}")
        return ""
    except requests.exceptions.RequestException as e:
        print(f"\n请求错误: {str(e)}")
        return ""
    except KeyError as e:
        print(f"\n响应字段缺失: {str(e)}")
        return ""
    except Exception as e:
        print(f"\n调用 Ollama 时发生未知错误: {str(e)}")
        traceback.print_exc()
        return ""

if __name__ == "__main__":
    # 使用示例, 设置 keep_alive 为 0 秒，请求后立即卸载
    generate_with_ollama(
        system_prompt="你是一个网络安全专家，用攻防案例解释概念",
        user_input="什么是零日漏洞？",
        keep_alive="1s" # 请求后立即卸载
    )