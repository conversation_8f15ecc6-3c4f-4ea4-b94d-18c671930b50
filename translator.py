import os
import re
import pysrt
from concurrent.futures import ThreadPoolExecutor, CancelledError
from call_ollama import generate_with_ollama
from typing import List, Dict
import config
import time
cfg=config.load_config()['translation']

model_name=cfg['model']
system_prompt=cfg['system_prompt']

class InterruptedError(Exception):
    """自定义异常，表示操作被用户中断"""
    pass

class SubtitleTranslator:
    def __init__(self, model_name: str = model_name, max_workers: int = 2, progress_callback=None):
        self.model_name = model_name  # 本地模型名称
        self.max_workers = max_workers  # 并发线程数（根据GPU显存调整）
        self.total_subs = 0  # 总字幕数
        self.translated_count = 0  # 已翻译数量
        self.progress_callback = progress_callback  # 进度回调函数
        # 常见英文词汇及其中文翻译对照表
        self.common_words = {
            "yeah": "是的",
            "yes": "是的",
            "no": "不",
            "ok": "好的",
            "okay": "好的",
            "hi": "嗨",
            "hello": "你好",
            "thanks": "谢谢",
            "thank you": "谢谢",
            "sorry": "抱歉",
            "excuse me": "打扰一下",
            "please": "请",
            "bye": "再见",
            "goodbye": "再见",
            "good morning": "早上好",
            "good afternoon": "下午好",
            "good evening": "晚上好",
            "good night": "晚安",
            "fuck": "操",
            "shit": "糟糕",
            "damn": "该死",
            "oh my god": "我的天啊",
            "wow": "哇",
            "cool": "酷",
            "awesome": "太棒了",
            "amazing": "太神奇了",
            "great": "很好",
            "nice": "不错",
            "well": "好吧",
            "so": "所以",
            "just": "只是",
            "like": "喜欢",
            "really": "真的",
            "very": "非常",
            "too": "也",
            "and": "和",
            "or": "或者",
            "but": "但是",
            "if": "如果",
            "because": "因为",
            "why": "为什么",
            "what": "什么",
            "when": "什么时候",
            "where": "哪里",
            "who": "谁",
            "how": "怎么样",
            "which": "哪个",
            "this": "这个",
            "that": "那个",
            "these": "这些",
            "those": "那些",
            "here": "这里",
            "there": "那里",
            "now": "现在",
            "then": "然后",
            "today": "今天",
            "tomorrow": "明天",
            "yesterday": "昨天"
        }
        
    def process_file(self, file_path: str, stopped_flag=None):
        file_path=str(file_path)
        """主处理流程"""
        # --- 检查停止标志 (开始时) ---
        if stopped_flag and stopped_flag():
            print("翻译操作已停止（在处理文件开始时）")
            return None
        # -----------------------------

        # 检查是否为视频文件，如果是则查找同名字幕文件
        video_extensions = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm']
        if any(file_path.lower().endswith(ext) for ext in video_extensions):
            print(f"检测到视频文件: {file_path}")
            # 查找同名字幕文件
            subtitle_extensions = ['.srt', '.ass', '.ssa', '.sub']
            base_path = os.path.splitext(file_path)[0]
            
            for ext in subtitle_extensions:
                subtitle_path = base_path + ext
                if os.path.exists(subtitle_path):
                    print(f"找到对应字幕文件: {subtitle_path}")
                    file_path = subtitle_path
                    break
            else:
                print(f"未找到对应的字幕文件，请先生成字幕文件")
                return None
        
        # 检查文件扩展名是否为字幕文件
        subtitle_extensions = ['.srt', '.ass', '.ssa', '.sub']
        if not any(file_path.lower().endswith(ext) for ext in subtitle_extensions):
            print(f"错误：{file_path} 不是支持的字幕文件格式")
            return None
        
        print(f"正在处理字幕文件: {file_path}")
        
        # --- 检查停止标志 (打开文件前) ---
        if stopped_flag and stopped_flag(): return None
        # -----------------------------

        # 尝试不同编码打开文件
        encodings = ['utf-8', 'latin1', 'cp1252', 'gbk']
        subs = None
        
        for encoding in encodings:
            try:
                subs = pysrt.open(file_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码打开文件")
                break
            except Exception as e:
                print(f"使用 {encoding} 编码打开文件失败: {e}")
        
        if subs is None:
            print("无法打开字幕文件，请检查文件格式和编码")
            return None
        
        self.total_subs = len(subs)
        print(f"共发现 {self.total_subs} 条字幕")
        
        # --- 检查停止标志 (清洗前) ---
        if stopped_flag and stopped_flag(): return None
        # ---------------------------

        # 清洗并准备翻译数据
        clean_subs = [{
            "index": sub.index,
            "timecode": self._extract_timecode(sub),
            "text": self._clean_text(sub.text)
        } for sub in subs]

        # --- 检查停止标志 (批量翻译前) ---
        if stopped_flag and stopped_flag(): return None
        # ---------------------------

        # 批量翻译（带上下文）
        print("开始翻译...")
        start_time = time.time()
        try:
            translated_subs = self._batch_translate(clean_subs, stopped_flag=stopped_flag)
        except InterruptedError:
            print("翻译操作已被中断")
            return None # Propagate interruption
        elapsed_time = time.time() - start_time
        print(f"翻译完成，耗时: {elapsed_time:.2f}秒")
        
        # --- 检查停止标志 (生成双语字幕前) ---
        if stopped_flag and stopped_flag(): return None
        # -------------------------------

        # 生成双语字幕
        print("正在生成双语字幕...")
        bilingual_subs = self._create_bilingual(clean_subs, translated_subs)
        
        # --- 检查停止标志 (保存前) ---
        if stopped_flag and stopped_flag(): return None
        # ---------------------------

        # 保存文件
        output_path=file_path
        self._save_srt(bilingual_subs, output_path)
        return output_path

    def _batch_translate(self, subs: List[Dict], stopped_flag=None) -> List[str]:
        """带上下文的批量翻译"""
        self.translated_count = 0
        results = [None] * len(subs)
        
        # --- 检查停止标志 (开始批量处理前) ---
        if stopped_flag and stopped_flag():
            raise InterruptedError("Batch translation stopped at the beginning")
        # ------------------------------------

        similarity_groups = self._identify_similar_subtitles(subs)
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {}
            for idx, sub in enumerate(subs):
                # --- 检查停止标志 (提交任务前) ---
                if stopped_flag and stopped_flag():
                    # 取消已提交的未来任务
                    for future in futures:
                        future.cancel()
                    raise InterruptedError("Batch translation stopped before submitting all tasks")
                # ------------------------------------

                skip = False
                for group_id, group_indices in similarity_groups.items():
                    if idx in group_indices and idx != min(group_indices):
                        # 不是组内第一个元素，复用翻译结果
                        skip = True
                        break
                
                if not skip:
                    context = self._get_context(subs, idx)
                    future = executor.submit(
                        self._translate_text,
                        text=sub["text"],
                        context=context,
                        index=idx,
                        stopped_flag=stopped_flag
                    )
                    futures[future] = idx
            
            # 处理完成的任务
            for future in futures:
                # --- 检查停止标志 (处理结果前) ---
                # 虽然future.result()会等待，但我们可以在等待前或后检查
                if stopped_flag and stopped_flag():
                    # 尝试取消剩余任务（可能有些已经完成）
                    for f in futures:
                         if not f.done(): f.cancel()
                    raise InterruptedError("Batch translation stopped while processing results")
                # ------------------------------------

                idx = futures[future]
                try:
                    result = future.result()
                    if result is None and stopped_flag and stopped_flag(): # 如果_translate_text因为停止返回None
                         raise InterruptedError("Task was stopped during translation")
                    elif result is None: # 其他原因返回None，视为失败
                         print(f"翻译字幕 #{idx+1} 返回 None，视为失败")
                         results[idx] = subs[idx]["text"]
                    else:
                         results[idx] = self._post_process_translation(result, subs[idx]["text"])
                    
                    # 处理相似字幕组
                    for group_id, group_indices in similarity_groups.items():
                        if idx == min(group_indices):  # 是组内第一个元素
                            # 复用翻译结果给组内其他成员
                            for other_idx in group_indices:
                                if other_idx != idx:
                                    results[other_idx] = results[idx]
                                    self.translated_count += 1
                                    self._show_progress()
                    
                except CancelledError:
                    print(f"翻译字幕 #{idx+1} 被取消")
                    results[idx] = subs[idx]["text"] # 被取消时使用原文
                except InterruptedError:
                     print(f"翻译字幕 #{idx+1} 被中断")
                     results[idx] = subs[idx]["text"]
                     raise # 重新抛出中断异常，以便外层捕获
                except Exception as e:
                    print(f"处理翻译结果 #{idx+1} 时出错: {str(e)}")
                    results[idx] = subs[idx]["text"]  # 失败时使用原文
                
                # 只有在未中断的情况下才增加计数和显示进度
                if not (stopped_flag and stopped_flag()):
                    self.translated_count += 1
                    self._show_progress()
                else:
                    # 如果已发出停止信号，即使有些任务完成，也认为已停止
                    # 确保上层能捕获 InterruptedError
                    if not isinstance(e, InterruptedError):
                        raise InterruptedError("Stop signal received while processing results")
            
            # 确保最终进度为100%
            if self.progress_callback and self.translated_count > 0:
                message = f"翻译完成: 共处理{self.translated_count}条字幕"
                self.progress_callback.update(100, message)
                
        return results
        
    def _identify_similar_subtitles(self, subs: List[Dict]) -> Dict[int, List[int]]:
        """识别相似或重复的字幕"""
        similarity_groups = {}
        group_id = 0
        
        # 寻找完全相同的字幕
        text_to_indices = {}
        for idx, sub in enumerate(subs):
            text = sub["text"].strip().lower()
            if text in text_to_indices:
                text_to_indices[text].append(idx)
            else:
                text_to_indices[text] = [idx]
        
        # 创建相似组
        for text, indices in text_to_indices.items():
            if len(indices) > 1:  # 有重复
                similarity_groups[group_id] = indices
                group_id += 1
        
        # 也可以扩展来识别相似(而非完全相同)的字幕
        # 这里可以使用更复杂的相似度算法
        
        return similarity_groups

    def _show_progress(self):
        """显示翻译进度"""
        if self.total_subs == 0:
            return
            
        percent = min((self.translated_count / self.total_subs) * 100, 100)
        
        # 如果有进度回调函数，则调用它
        if self.progress_callback:
            # 控制更新频率，避免过于频繁导致UI卡顿
            now = time.time()
            # 更新间隔与显示阈值
            update_interval = 0.1  # 更新间隔，秒
            force_update = self.translated_count == 1 or self.translated_count == self.total_subs or self.translated_count % max(1, self.total_subs // 50) == 0
            
            if not hasattr(self, '_last_progress_update') or now - self._last_progress_update > update_interval or percent >= 100 or percent == 0 or force_update:
                message = f"翻译进度: {percent:.1f}% ({self.translated_count}/{self.total_subs})"
                # 直接传递百分比值并确保在主线程更新UI
                self.progress_callback.update(float(percent), message)
                self._last_progress_update = now
                self._last_progress = percent
        else:
            # 如果没有回调，则使用命令行显示进度
            bar_length = 30
            filled_length = int(bar_length * self.translated_count // self.total_subs)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            print(f"\r翻译进度: [{bar}] {percent:.1f}% ({self.translated_count}/{self.total_subs})", end='')
            if self.translated_count == self.total_subs:
                print()  # 完成后换行

    def _translate_text(self, text: str, context: dict, index: int, stopped_flag=None) -> str:
        """调用本地Ollama模型进行翻译"""
        # --- 检查停止标志 (调用Ollama前) ---
        if stopped_flag and stopped_flag():
            print(f"翻译字幕 #{index+1} 在调用模型前被停止")
            return None # 返回 None 表示被停止
        # -----------------------------------

        system_prompt = self._build_system_prompt(context)
        user_prompt = 'no-think\n'+self._build_user_prompt(text, context)
        
        try:
            result = generate_with_ollama(
                system_prompt=system_prompt,
                user_input=user_prompt,
                model=self.model_name
            )
            # 再次检查，以防在Ollama调用期间停止标志被设置
            if stopped_flag and stopped_flag():
                print(f"翻译字幕 #{index+1} 在调用模型后检测到停止")
                return None
            return self._clean_translation_result(result, text)
        except Exception as e:
            # 检查是否因为停止而引发异常（虽然目前generate_with_ollama不支持）
            if stopped_flag and stopped_flag():
                 print(f"翻译字幕 #{index+1} 在调用模型中被停止（通过异常）")
                 return None
            print(f"\n翻译字幕 #{index+1} 失败: {str(e)}")
            return text  # 失败时返回原文

    def _clean_translation_result(self, result: str, original_text: str) -> str:
        """清理翻译结果，移除可能的重复内容或多余标记"""
        # 移除可能的前缀标签如"翻译结果："、"中文翻译："等
        result = re.sub(r'^(翻译结果|中文翻译|翻译|译文)[:：]\s*', '', result.strip())
        
        # 只保留第一行，忽略可能的解释或多行输出
        result = result.split('\n')[-1].strip()
        
        # 如果翻译结果超过原文长度的2倍，可能包含了上下文，尝试识别核心部分
        if len(result) > len(original_text) * 2:
            # 尝试基于语义相似度找出最匹配原文的句子
            sentences = re.split(r'[。！？.!?]+', result)
            if len(sentences) > 1:
                # 找出长度与原文最匹配的句子
                best_match = max(sentences, key=lambda s: min(len(s)/len(original_text), len(original_text)/len(s)) if len(s) > 0 else 0)
                if len(best_match) > len(original_text) * 0.5:  # 至少有原文一半长
                    result = best_match
        
        return result

    def _post_process_translation(self, translated_text: str, original_text: str) -> str:
        """后处理翻译结果，修复中英夹杂问题"""
        # 如果翻译结果为空，返回原文
        if not translated_text or translated_text.strip() == "":
            return original_text
            
        # 检测英文单词或短语（排除人名、地名等专有名词的情况）
        def replace_english_words(match):
            word = match.group(0).lower()
            # 检查是否在常见词汇表中
            if word in self.common_words:
                return self.common_words[word]
            # 如果是单个字母或数字，保留原样
            if len(word) <= 1 or word.isdigit():
                return match.group(0)
            # 如果是大写开头（可能是专有名词），保留原样
            if match.group(0)[0].isupper() and len(word) > 1:
                return match.group(0)
            # 其他情况，尝试二次翻译
            try:
                second_translation = generate_with_ollama(
                    system_prompt="你是一名专业翻译，请直接给出翻译结果，不要有任何解释或额外文字。",
                    user_input=f"请将英文单词'{word}'翻译成中文，直接给出中文结果，不要解释。",
                    model=self.model_name
                )
                return second_translation.strip() if second_translation.strip() else match.group(0)
            except:
                return match.group(0)
        
        # 使用正则表达式查找英文单词或短语
        # 排除已经在中文括号内的英文（这些通常是有意保留的）
        pattern = r'(?<!\（)[a-zA-Z]+(?:\s+[a-zA-Z]+)*(?!\）)'
        processed_text = re.sub(pattern, replace_english_words, translated_text)
        
        return processed_text

    def _build_system_prompt(self, context: dict) -> str:
        """构建系统提示词"""
        return f"""你是一名专业字幕翻译专家，你的任务是将英文字幕完全翻译成中文。请严格遵守以下规则：
                    1. 将所有英文内容翻译为地道、自然的中文，不允许保留任何英文
                    2. 绝对禁止在翻译结果中出现任何英文单词、字母或英文缩写
                    3. 译文长度不超过{context['max_length']}字符
                    4. 理解上下文，但只翻译用户指定的[待译]部分，不要翻译前文和后文
                    5. 人名、地名、品牌名等专有名词使用官方中文译名或通用中文译法
                    6. 输出结果必须是纯中文，符合中文口语习惯
                    7. 直接输出翻译结果，不要有任何解释或说明
                    8. 即使是口语化、非正式的表达也必须翻译成中文对应表达
                    9. 不要尝试解释或扩展原文内容，保持准确简洁
                    10. 保持字幕语气的一致性，理解但不复制上下文
                """

    def _build_user_prompt(self, text: str, context: dict) -> str:
        """构建用户提示词"""
        return f"""翻译影视字幕（必须完全转换为中文）：
                请仅将中间[待译]部分的英文文本翻译为纯中文，[前文]和[后文]仅作为参考，不要翻译它们。
                
                <翻译要求>
                1. 必须完全使用中文，绝对禁止出现任何英文单词、字母或缩写
                2. 人名/地名/品牌名必须使用标准中文译名
                3. 所有专业术语必须转化为中文术语
                4. 所有口语化表达必须转换为自然中文
                5. 即使是非常口语化、随意的表达也必须完全翻译成中文
                6. 如遇无法直接翻译的词语，使用意译而非保留原文
                7. 只输出[待译]部分的翻译结果，不要包含[前文]和[后文]的内容
                8. 如果[待译]是前文的延续，请保持连贯性，但不要重复前文内容
                9. 如果[待译]与[后文]有关联，理解上下文但仅翻译[待译]
                10. 输出格式必须是一行纯中文，没有英文、没有解释、没有"翻译结果："等前缀
                </翻译要求>
    
[前文] {context['prev_text']}
[待译] {text}
[后文] {context['next_text']}

记住：1. 只翻译[待译]部分，不要翻译前文和后文
2. 翻译结果中绝对不能出现任何英文单词或字母！
3. 输出必须是单行纯中文，没有任何多余内容"""

    def _get_context(self, subs: List[Dict], idx: int) -> dict:
        """获取上下文信息"""
        # 智能上下文处理：只提供相关的上下文
        
        # 获取前一个字幕文本
        prev_text = ""
        if idx > 0:
            prev_sub = subs[idx-1]["text"]
            # 分析前文与当前字幕的关系，例如检查是否为同一句话的延续
            if prev_sub.strip().endswith(('...', '…', '-', ',')):
                # 可能是连续的对话，提供完整前文
                prev_text = prev_sub
            else:
                # 否则只提供可能有用的关键信息
                prev_words = prev_sub.split()
                # 只取最后几个词作为上下文
                prev_text = ' '.join(prev_words[-min(5, len(prev_words)):])
        
        # 获取后一个字幕文本
        next_text = ""
        if idx < len(subs) - 1:
            next_sub = subs[idx+1]["text"]
            # 如果当前字幕以不完整标志结束，后文可能是其延续
            if subs[idx]["text"].strip().endswith(('...', '…', '-', ',')):
                next_text = next_sub
            else:
                # 否则只取开头几个词
                next_words = next_sub.split()
                next_text = ' '.join(next_words[:min(5, len(next_words))])
        
        return {
            "current_index": subs[idx]["index"],
            "prev_text": prev_text,
            "next_text": next_text,
            "max_length": int(len(subs[idx]["text"])*1.2)
        }

    def _create_bilingual(self, originals: List[Dict], translations: List[str]) -> List[Dict]:
        """创建双语字幕结构"""
        return [{
            "index": orig["index"],
            "timecode": orig["timecode"],
            "text": f"{orig['text']}\n{trans.split('\n')[0]}"  # 只保留翻译结果的第一行
        } for orig, trans in zip(originals, translations)]

    def _clean_text(self, text: str) -> str:
        """清洗字幕文本"""
        text = re.sub(r"\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3}", "", text)
        return re.sub(r"\s+", " ", text).strip()

    def _extract_timecode(self, sub) -> str:
        """提取时间码"""
        return f"{sub.start} --> {sub.end}"

    def _get_output_path(self, file_path: str) -> str:
        """生成输出路径"""
        base, ext = os.path.splitext(file_path)
        return f"{base}_bilingual{ext}"

    def _save_srt(self, subs: List[Dict], output_path: str):
        """保存为SRT文件"""
        new_subs = pysrt.SubRipFile()
        for sub in subs:
            item = pysrt.SubRipItem(
                index=sub["index"],
                start=pysrt.SubRipTime.from_string(sub["timecode"].split(' --> ')[0]),
                end=pysrt.SubRipTime.from_string(sub["timecode"].split(' --> ')[1]),
                text=sub["text"]
            )
            new_subs.append(item)
        new_subs.save(output_path, encoding='utf-8')

    def retranslate_subtitle(self, file_path: str, max_iterations: int = 5, stopped_flag=None):
        file_path=str(file_path)
        """
        重新翻译字幕文件中未翻译的内容
        
        Args:
            file_path: 字幕文件或视频文件路径
            max_iterations: 最大迭代次数，防止无限循环
            stopped_flag: 用于检查是否应停止处理的回调函数
        
        Returns:
            output_path: 处理后的字幕文件路径
        """
        # --- 检查停止标志 (开始时) ---
        if stopped_flag and stopped_flag():
            print("重新翻译操作已停止（开始时）")
            return None
        # -----------------------------

        # 检查是否为视频文件，如果是则查找同名字幕文件
        video_extensions = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm']
        if any(file_path.lower().endswith(ext) for ext in video_extensions):
            print(f"检测到视频文件: {file_path}")
            # 查找同名字幕文件
            subtitle_extensions = ['.srt', '.ass', '.ssa', '.sub']
            base_path = os.path.splitext(file_path)[0]
            
            for ext in subtitle_extensions:
                subtitle_path = base_path + ext
                if os.path.exists(subtitle_path):
                    print(f"找到对应字幕文件: {subtitle_path}")
                    file_path = subtitle_path
                    break
            else:
                print(f"未找到对应的字幕文件，请先生成字幕文件")
                return None
        
        # 检查文件扩展名是否为字幕文件
        subtitle_extensions = ['.srt', '.ass', '.ssa', '.sub']
        if not any(file_path.lower().endswith(ext) for ext in subtitle_extensions):
            print(f"错误：{file_path} 不是支持的字幕文件格式")
            return None
        
        # 目前只支持SRT格式处理
        if not file_path.lower().endswith('.srt'):
            print(f"警告：目前只支持SRT格式字幕，{file_path} 可能无法正确处理")
        
        print(f"正在处理字幕文件: {file_path}")
        
        # --- 检查停止标志 (检查双语前) ---
        if stopped_flag and stopped_flag(): return None
        # -----------------------------

        # 使用 utill.py 中的函数检查是否为双语字幕
        try:
            from utill import is_bilingual_subtitle
            is_bilingual = is_bilingual_subtitle(file_path)
        except Exception as e:
            print(f"检查双语字幕时出错: {e}")
            print("尝试使用不同编码打开文件...")
            try:
                # 尝试使用不同编码打开文件
                encodings = ['utf-8', 'latin1', 'cp1252', 'gbk']
                for encoding in encodings:
                    try:
                        subs = pysrt.open(file_path, encoding=encoding)
                        print(f"成功使用 {encoding} 编码打开文件")
                        is_bilingual = any('\n' in sub.text for sub in subs[:10])
                        break
                    except Exception:
                        continue
                else:
                    print("所有编码尝试失败")
                    return None
            except Exception as e2:
                print(f"无法打开字幕文件: {e2}")
                return None
        
        if not is_bilingual:
            print("检测到非双语字幕，将进行完整翻译...")
            # --- 检查停止标志 (调用process_file前) ---
            if stopped_flag and stopped_flag(): return None
            # -----------------------------------------
            return self.process_file(file_path, stopped_flag=stopped_flag)
        
        print("检测到双语字幕，开始查找未翻译内容...")
        
        # --- 检查停止标志 (读取字幕前) ---
        if stopped_flag and stopped_flag(): return None
        # -----------------------------
        subs = pysrt.open(file_path)
        
        iteration = 0
        while iteration < max_iterations:
            # --- 检查停止标志 (每轮循环开始时) ---
            if stopped_flag and stopped_flag():
                print(f"重新翻译操作已停止（在第 {iteration+1} 轮开始时）")
                return None
            # ---------------------------------------
            iteration += 1
            print(f"\n开始第 {iteration} 轮翻译修复...")
            
            # 查找未翻译内容
            fully_untranslated = []  # 整句未翻译
            partially_untranslated = []  # 部分未翻译
            
            for sub in subs:
                lines = sub.text.strip().split('\n')
                if len(lines) < 2:
                    # 只有一行，可能是未翻译的
                    fully_untranslated.append({
                        "index": sub.index,
                        "timecode": self._extract_timecode(sub),
                        "text": lines[0],
                        "sub_obj": sub
                    })
                else:
                    # 判断哪一行是英文，哪一行是中文
                    from utill import is_chinese_text, is_english
                    
                    # 检查第一行是否为中文
                    first_is_chinese = is_chinese_text(lines[0])
                    last_is_chinese = is_chinese_text(lines[-1]) if len(lines) > 1 else False
                    
                    # 如果第一行是中文，检查是否包含英文（部分未翻译）
                    if first_is_chinese and is_english(lines[0]):
                        partially_untranslated.append({
                            "index": sub.index,
                            "timecode": self._extract_timecode(sub),
                            "text": lines[-1] if is_english(lines[-1]) else lines[0],
                            "translation": lines[0],
                            "sub_obj": sub,
                            "chinese_line_index": 0  # 中文在第一行
                        })
                    # 如果最后一行是中文，检查是否包含英文（部分未翻译）
                    elif last_is_chinese and is_english(lines[-1]):
                        partially_untranslated.append({
                            "index": sub.index,
                            "timecode": self._extract_timecode(sub),
                            "text": lines[0] if is_english(lines[0]) else lines[-1],
                            "translation": lines[-1],
                            "sub_obj": sub,
                            "chinese_line_index": -1  # 中文在最后一行
                        })
                    # 如果没有中文行，则认为是完全未翻译
                    elif not first_is_chinese and not last_is_chinese:
                        fully_untranslated.append({
                            "index": sub.index,
                            "timecode": self._extract_timecode(sub),
                            "text": lines[0],  # 使用第一行作为原文
                            "sub_obj": sub
                        })
            
            total_subs = len(subs)
            fully_percent = len(fully_untranslated) / total_subs * 100
            partially_percent = len(partially_untranslated) / total_subs * 100
            
            print(f"发现 {len(fully_untranslated)} 条完全未翻译字幕 ({fully_percent:.2f}%)")
            print(f"发现 {len(partially_untranslated)} 条部分未翻译字幕 ({partially_percent:.2f}%)")
            
            # 使用进度回调更新总体进度
            if self.progress_callback:
                iteration_progress = (iteration - 1) / max_iterations * 100
                self.progress_callback.update(iteration_progress, 
                    f"修复翻译第 {iteration}/{max_iterations} 轮: 发现 {len(fully_untranslated) + len(partially_untranslated)} 条需要处理")
            
            # 检查是否达到退出条件
            if fully_percent < 1 and partially_percent < 3:
                print("未翻译内容已低于阈值，翻译修复完成")
                break
            
            # --- 检查停止标志 (翻译前) ---
            if stopped_flag and stopped_flag(): return None
            # ---------------------------

            # 处理完全未翻译的字幕
            if fully_untranslated:
                print("开始翻译完全未翻译的字幕...")
                self.total_subs = len(fully_untranslated)
                self.translated_count = 0
                
                try:
                    clean_subs = fully_untranslated
                    # --- 检查停止标志 (调用_batch_translate前) ---
                    if stopped_flag and stopped_flag(): return None
                    # -----------------------------------------
                    translated_texts = self._batch_translate(clean_subs, stopped_flag=stopped_flag)
                    # --- 检查停止标志 (更新字幕前) ---
                    if stopped_flag and stopped_flag(): return None
                    # -----------------------------
                    for i, sub_data in enumerate(fully_untranslated):
                        sub = sub_data["sub_obj"]
                        translation = translated_texts[i].split('\n')[0] if translated_texts[i] else sub_data["text"] # Handle None translation
                        sub.text = f"{sub_data['text']}\n{translation}"
                except InterruptedError:
                    print("完全未翻译部分的处理被中断")
                    return None
            
            # 处理部分未翻译的字幕
            if partially_untranslated:
                print("开始修复部分未翻译的字幕...")
                self.total_subs = len(partially_untranslated)
                self.translated_count = 0
                
                try:
                    with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                        futures = {}
                        for idx, sub_data in enumerate(partially_untranslated):
                            # --- 检查停止标志 (提交任务前) ---
                            if stopped_flag and stopped_flag():
                                for future in futures: future.cancel()
                                raise InterruptedError("Partial retranslation stopped before submitting all tasks")
                            # ------------------------------------
                            context = self._get_context_from_subs(subs, sub_data["index"])
                            future = executor.submit(
                                self._translate_text,
                                text=sub_data["text"],
                                context=context,
                                index=idx,
                                stopped_flag=stopped_flag # <--- 传递 stopped_flag
                            )
                            futures[future] = idx
                        
                        # 处理完成的任务
                        for future in futures:
                            # --- 检查停止标志 (处理结果前) ---
                            if stopped_flag and stopped_flag():
                                for f in futures: 
                                     if not f.done(): f.cancel()
                                raise InterruptedError("Partial retranslation stopped while processing results")
                            # ------------------------------------
                            idx = futures[future]
                            try:
                                result = future.result()
                                if result is None and stopped_flag and stopped_flag():
                                    raise InterruptedError("Task was stopped during translation")
                                elif result is None:
                                    print(f"部分翻译字幕 #{idx+1} 返回 None，跳过更新")
                                    continue # 跳过这个字幕的更新
                                else:
                                    fixed_translation = self._post_process_translation(result, partially_untranslated[idx]["text"])
                                    sub = partially_untranslated[idx]["sub_obj"]
                                    fixed_translation = fixed_translation.split('\n')[0]
                                    if partially_untranslated[idx].get("chinese_line_index") == 0:
                                        sub.text = f"{fixed_translation}\n{partially_untranslated[idx]['text']}"
                                    else:
                                        sub.text = f"{partially_untranslated[idx]['text']}\n{fixed_translation}"

                            except CancelledError:
                                print(f"部分翻译字幕 #{idx+1} 被取消")
                            except InterruptedError:
                                print(f"部分翻译字幕 #{idx+1} 被中断")
                                raise # 重新抛出
                            except Exception as e:
                                print(f"处理部分翻译结果 #{idx+1} 时出错: {str(e)}")

                            if not (stopped_flag and stopped_flag()):
                                self.translated_count += 1
                                self._show_progress()
                            else:
                                 if not isinstance(e, InterruptedError): # Don't suppress the original interrupt
                                      raise InterruptedError("Stop signal received while processing partial results")
                except InterruptedError:
                    print("部分未翻译部分的处理被中断")
                    return None
            
            # --- 检查停止标志 (保存前) ---
            if stopped_flag and stopped_flag(): return None
            # ---------------------------
            subs.save(file_path, encoding='utf-8')
            
            # 更新整体进度
            if self.progress_callback:
                iteration_progress = iteration / max_iterations * 100
                self.progress_callback.update(iteration_progress, 
                    f"修复翻译完成第 {iteration}/{max_iterations} 轮，剩余待处理: {len(fully_untranslated) + len(partially_untranslated)} 条")
        
        # 确保最终进度更新到100%
        if self.progress_callback:
            self.progress_callback.update(100, f"字幕修复完成，共进行了 {iteration} 轮翻译")
        
        print(f"字幕修复完成，共进行了 {iteration} 轮翻译")
        return file_path

    def _get_context_from_subs(self, subs, current_index):
        """从字幕对象获取上下文"""
        idx = current_index - 1  # 索引从1开始，转为从0开始
        prev_text = subs[idx-1].text.split('\n')[0] if idx > 0 else ""
        next_text = subs[idx+1].text.split('\n')[0] if idx < len(subs)-1 else ""
        
        return {
            "current_index": current_index,
            "prev_text": prev_text,
            "next_text": next_text,
            "max_length": int(len(subs[idx].text.split('\n')[0])*1.2)
        }

if __name__ == "__main__":
    import sys
    
    # 获取输入文件路径
    def get_input_file():
        if len(sys.argv) >= 2:
            # 有命令行参数，使用第一个参数作为文件路径
            return sys.argv[1]
        else:
            # 没有命令行参数，提示用户输入
            return input('请输入需要转录的视频或音频文件路径: ').replace('"', '').strip()
    
    # 创建翻译器实例
    def create_translator():
        return SubtitleTranslator(
            model_name=model_name,
            max_workers=1
        )
    
    # 处理文件
    def process_input_file(file_path):
        translator = create_translator()
        result_path = translator.process_file(file_path)
        if result_path:
            print(f"生成双语字幕文件：{result_path}")
        else:
            print("处理失败")
        return result_path
    
    # 主执行流程
    try:
        input_file = get_input_file()
        if input_file:
            process_input_file(input_file)
        else:
            print("未提供有效的文件路径")
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
