# 字幕处理系统流程文档



## 主流程分析

### 1. 目录处理流程
```mermaid (process_directory())
graph LR
a[输入目录路径] --> b[历遍目录下所有文件]
b -->c{文件类型判断}
c -->|视频文件或字幕文件|d[单文件处理流程]
c -->|其他文件|f[跳过]
```

### 2. 单文件处理流程
```mermaid (process_single_file())
graph LR

    A[输入文件路径] --> B{文件类型判断}
    B -->|视频文件| C[视频文件处理流程]
    B -->|字幕文件| D[字幕文件处理流程]
```
#### 2.1 视频文件处理流程
```mermaid (process_video())
graph LR
    A[输入视频路径] --> B{判断是否已有字幕文件}
    B -->|没有字幕文件|C[生成字幕流程]
        C --> D[字幕文件处理流程]
    B -->|已有字幕文件|D[字幕文件处理流程]

```

##### 2.1.1 生成字幕流程
```mermaid (generate_subtitle())
graph LR
    A[输入视频路径] --> B[调用whisper-fast模型进行语音识别]
    B --> C[生成.srt文件]
    C --> D[返回字幕文件路径]
```
#### 2.2 字幕文件处理流程
```mermaid (process_subtitle())
graph LR
A[输入字幕文件路径] --> B{判断是否双语字幕}
    B -->|是双语字幕|C[双语处理流程]
    B -->|不是双语字幕|D[单语处理流程]
```

##### 2.2.1 单语处理流程
```mermaid (process_single_language())
graph LR
    A[输入字幕文件路径] --> a{判断是否中文字幕}
        a --> |是| E[跳过，打印消息]
        a --> |否| B[优化字幕流程]
    B --> C[翻译字幕流程]
    C --> D[合并处理结果流程]

```
##### 2.2.2 双语处理流程
```mermaid (process_single_language())
graph LR
    A[输入字幕文件路径] --> a{双语字幕是否需要拆分}
        a --> |是| B[拆分双语字幕流程]
            B --> C[丢弃中文字幕，返回原语言字幕]
            C --> D[单语处理流程]
        a --> |否| E[跳过，打印消息]


```

##### 2.2.3 优化字幕流程
```mermaid (optimize_srt())
graph LR
    A[输入字幕文件路径] --> B[读取字幕文件]
    B --> C[合并相邻短句]
    C --> D[删除包含无效词的字幕项]
    D --> E[保存覆盖源文件]
```

##### 2.2.4 翻译字幕流程
```mermaid (translate_subtitle())
graph LR
    A[输入字幕文件路径] --> B[读取字幕文件]
    B --> C[翻译字幕流程]
    C --> D[生成翻译文件]
```




### 4. 核心模块说明


#### 关键配置项
```python
self.config = {
    'video_extensions': ['.mp4', '.mkv', ...],
    'subtitle_extensions': ['.srt', '.ass'],
    'pointless_words_file': 'Pointless_word.txt',
    'auto_pack': False  # 通过toggle_auto_pack()切换
}
```

## 分支流程说明

### 1. 字幕生成流程
```python
def generate_subtitle_by_xxl(video_path):
    使用whisper-fast模型进行语音识别
    输出格式：视频同名.srt文件
    参数配置：whisper_config.yaml
```

### 2. 字幕优化流程
```python
def optimize_srt(input_path):
    1. 加载Pointless_word.txt中的无效词列表
    2. 合并相邻短句（<2秒间隔）
    3. 删除包含无效词的字幕项
    4. 生成.optimized临时文件
```

### 3. 双语处理流程
```python
def process_bilingual(input_path):
    1. split_subtitles() 拆分双语字幕
    2. 分别处理原始字幕和翻译字幕
    3. merge_subtitles() 合并处理结果
```


