#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕质量优化模块
专门用于检测和重新转写长时间字幕片段，提高字幕质量
"""

import subprocess
import os
import tempfile
import shutil
from pathlib import Path
import config
from utill import timer
from generate_subtitles import use_faster_whisper_xxl


def parse_srt_file(srt_path):
    """解析SRT文件，返回字幕条目列表"""
    subtitles = []
    try:
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        # 分割字幕块
        blocks = content.split('\n\n')
        
        for block in blocks:
            if not block.strip():
                continue
                
            lines = block.strip().split('\n')
            if len(lines) < 3:
                continue
                
            try:
                # 解析序号
                index = int(lines[0])
                
                # 解析时间戳
                time_line = lines[1]
                start_time, end_time = time_line.split(' --> ')
                
                # 解析文本内容
                text = '\n'.join(lines[2:])
                
                subtitles.append({
                    'index': index,
                    'start_time': start_time,
                    'end_time': end_time,
                    'text': text,
                    'duration': calculate_duration(start_time, end_time)
                })
            except (ValueError, IndexError) as e:
                print(f"解析字幕块时出错: {e}, 块内容: {block}")
                continue
                
    except Exception as e:
        print(f"解析SRT文件时出错: {e}")
        
    return subtitles


def calculate_duration(start_time, end_time):
    """计算字幕持续时间（秒）"""
    def time_to_seconds(time_str):
        # 格式: HH:MM:SS,mmm
        time_part, ms_part = time_str.split(',')
        h, m, s = map(int, time_part.split(':'))
        ms = int(ms_part)
        return h * 3600 + m * 60 + s + ms / 1000.0
    
    return time_to_seconds(end_time) - time_to_seconds(start_time)


def find_long_segments(subtitles, threshold=10.0):
    """找出持续时间超过阈值的字幕片段"""
    long_segments = []
    for subtitle in subtitles:
        if subtitle['duration'] > threshold:
            long_segments.append(subtitle)
    return long_segments


def convert_srt_time_to_ffmpeg(srt_time):
    """将SRT时间格式转换为FFmpeg格式"""
    # SRT格式: HH:MM:SS,mmm
    # FFmpeg格式: HH:MM:SS.mmm
    return srt_time.replace(',', '.')


def extract_audio_segment(video_path, start_time, end_time, output_path):
    """从视频中提取指定时间段的音频"""
    try:
        # 转换时间格式
        ffmpeg_start_time = convert_srt_time_to_ffmpeg(start_time)
        ffmpeg_end_time = convert_srt_time_to_ffmpeg(end_time)

        print(f"提取音频片段: {ffmpeg_start_time} --> {ffmpeg_end_time}")

        cmd = [
            "ffmpeg",
            "-i", str(video_path),
            "-ss", ffmpeg_start_time,
            "-to", ffmpeg_end_time,
            "-vn",  # 不包含视频
            "-acodec", "pcm_s16le",  # 使用PCM编码
            "-ar", "16000",  # 采样率16kHz
            "-ac", "1",  # 单声道
            "-y",  # 覆盖输出文件
            str(output_path)
        ]

        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        if result.returncode == 0:
            print(f"成功提取音频片段: {output_path}")
            return True
        else:
            print(f"提取音频片段失败: {result.stderr}")
            return False

    except Exception as e:
        print(f"提取音频片段时出错: {e}")
        return False


def retranscribe_audio_segment(audio_path, model, stopped_flag=None):
    """重新转写音频片段"""
    try:
        # 检查停止标志
        if stopped_flag and stopped_flag():
            return None
            
        # 使用现有的faster-whisper-xxl函数进行转写
        result = use_faster_whisper_xxl(audio_path, model=model, stopped_flag=stopped_flag)
        
        if result and os.path.exists(result):
            return result
        else:
            print(f"重新转写音频片段失败: {audio_path}")
            return None
            
    except Exception as e:
        print(f"重新转写音频片段时出错: {e}")
        return None


def time_to_seconds(time_str):
    """将时间字符串转换为秒数"""
    time_part, ms_part = time_str.split(',')
    h, m, s = map(int, time_part.split(':'))
    ms = int(ms_part)
    return h * 3600 + m * 60 + s + ms / 1000.0


def seconds_to_time(seconds):
    """将秒数转换为时间字符串"""
    h = int(seconds // 3600)
    m = int((seconds % 3600) // 60)
    s = int(seconds % 60)
    ms = int((seconds % 1) * 1000)
    return f"{h:02d}:{m:02d}:{s:02d},{ms:03d}"


def adjust_optimized_timestamps(optimized_subs, original_start, original_end):
    """调整优化后字幕的时间戳，使其在原始时间范围内"""
    if not optimized_subs:
        return []
    
    original_start_sec = time_to_seconds(original_start)
    original_end_sec = time_to_seconds(original_end)
    original_duration = original_end_sec - original_start_sec
    
    # 获取优化后字幕的总时长
    first_start = time_to_seconds(optimized_subs[0]['start_time'])
    last_end = time_to_seconds(optimized_subs[-1]['end_time'])
    optimized_duration = last_end - first_start
    
    # 计算时间缩放比例
    scale_factor = original_duration / optimized_duration if optimized_duration > 0 else 1.0
    
    adjusted_subs = []
    for sub in optimized_subs:
        sub_start_sec = time_to_seconds(sub['start_time'])
        sub_end_sec = time_to_seconds(sub['end_time'])
        
        # 相对于优化字幕开始时间的偏移
        start_offset = (sub_start_sec - first_start) * scale_factor
        end_offset = (sub_end_sec - first_start) * scale_factor
        
        # 调整到原始时间范围内
        new_start_sec = original_start_sec + start_offset
        new_end_sec = original_start_sec + end_offset
        
        # 确保不超出原始时间范围
        new_start_sec = max(original_start_sec, min(new_start_sec, original_end_sec))
        new_end_sec = max(original_start_sec, min(new_end_sec, original_end_sec))
        
        adjusted_subs.append({
            'index': sub['index'],
            'start_time': seconds_to_time(new_start_sec),
            'end_time': seconds_to_time(new_end_sec),
            'text': sub['text'],
            'duration': new_end_sec - new_start_sec
        })
    
    return adjusted_subs


def merge_optimized_segments(original_subtitles, optimized_segments, long_segments):
    """将优化后的字幕片段合并回原始字幕"""
    # 创建原始字幕的副本
    merged_subtitles = original_subtitles.copy()
    
    # 为每个长片段替换优化后的内容
    for i, long_segment in enumerate(long_segments):
        if i < len(optimized_segments) and optimized_segments[i]:
            # 解析优化后的字幕
            optimized_subs = parse_srt_file(optimized_segments[i])
            
            if optimized_subs:
                # 调整优化后字幕的时间戳，使其在原始时间范围内
                adjusted_subs = adjust_optimized_timestamps(
                    optimized_subs, 
                    long_segment['start_time'], 
                    long_segment['end_time']
                )
                
                # 在合并列表中找到并替换对应的长片段
                for j, subtitle in enumerate(merged_subtitles):
                    if subtitle['index'] == long_segment['index']:
                        # 删除原始长片段
                        merged_subtitles.pop(j)
                        # 在相同位置插入优化后的片段
                        for k, adj_sub in enumerate(adjusted_subs):
                            merged_subtitles.insert(j + k, adj_sub)
                        break
    
    # 重新排序和编号
    merged_subtitles.sort(key=lambda x: time_to_seconds(x['start_time']))
    for i, subtitle in enumerate(merged_subtitles):
        subtitle['index'] = i + 1
    
    return merged_subtitles


def write_srt_file(subtitles, output_path):
    """将字幕列表写入SRT文件"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            for subtitle in subtitles:
                f.write(f"{subtitle['index']}\n")
                f.write(f"{subtitle['start_time']} --> {subtitle['end_time']}\n")
                f.write(f"{subtitle['text']}\n\n")
        return True
    except Exception as e:
        print(f"写入SRT文件时出错: {e}")
        return False


@timer
def optimize_subtitle_quality(srt_path, video_path=None, stopped_flag=None):
    """
    字幕质量优化主函数

    参数:
        srt_path: 字幕文件路径
        video_path: 视频文件路径（可选，如果不提供会尝试自动推断）
        stopped_flag: 停止标志函数

    返回:
        优化后的字幕文件路径
    """
    try:
        # 获取配置
        cfg = config.load_config()
        quality_config = cfg.get('subtitle_quality', {})

        # 检查是否启用优化
        if not quality_config.get('enable_optimization', True):
            print("字幕质量优化已禁用")
            return srt_path

        # 检查停止标志
        if stopped_flag and stopped_flag():
            return srt_path

        print("开始字幕质量优化...")

        # 获取配置参数
        duration_threshold = quality_config.get('duration_threshold', 10.0)
        optimization_model = quality_config.get('optimization_model', 'large-v2')

        # 如果没有提供视频路径，尝试自动推断
        if video_path is None:
            video_path = find_corresponding_video(srt_path)
            if video_path is None:
                print("无法找到对应的视频文件，跳过字幕质量优化")
                return srt_path

        print(f"使用视频文件: {video_path}")
        print(f"使用优化模型: {optimization_model}")
        print(f"时长阈值: {duration_threshold}秒")

        # 解析原始字幕文件
        print(f"解析字幕文件: {srt_path}")
        original_subtitles = parse_srt_file(srt_path)

        if not original_subtitles:
            print("无法解析字幕文件，跳过优化")
            return srt_path

        # 找出长时间片段
        long_segments = find_long_segments(original_subtitles, duration_threshold)

        if not long_segments:
            print(f"未找到持续时间超过 {duration_threshold} 秒的字幕片段，无需优化")
            return srt_path

        print(f"找到 {len(long_segments)} 个需要优化的长时间片段")

        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="subtitle_optimization_")
        print(f"创建临时目录: {temp_dir}")

        try:
            optimized_segments = []

            for i, segment in enumerate(long_segments):
                # 检查停止标志
                if stopped_flag and stopped_flag():
                    print("优化过程被中断")
                    return srt_path

                print(f"优化片段 {i+1}/{len(long_segments)}: {segment['start_time']} --> {segment['end_time']} (时长: {segment['duration']:.1f}秒)")

                # 提取音频片段
                audio_filename = f"segment_{i+1}.wav"
                audio_path = os.path.join(temp_dir, audio_filename)

                if extract_audio_segment(video_path, segment['start_time'], segment['end_time'], audio_path):
                    # 检查停止标志
                    if stopped_flag and stopped_flag():
                        print("优化过程被中断")
                        return srt_path

                    # 重新转写音频片段
                    print(f"使用模型 {optimization_model} 重新转写片段 {i+1}")
                    optimized_srt = retranscribe_audio_segment(audio_path, optimization_model, stopped_flag)

                    if optimized_srt:
                        optimized_segments.append(optimized_srt)
                        print(f"片段 {i+1} 优化完成")
                    else:
                        print(f"片段 {i+1} 优化失败，保留原始内容")
                        optimized_segments.append(None)
                else:
                    print(f"片段 {i+1} 音频提取失败，保留原始内容")
                    optimized_segments.append(None)

            # 检查是否有成功优化的片段
            successful_optimizations = sum(1 for seg in optimized_segments if seg is not None)

            if successful_optimizations == 0:
                print("没有成功优化任何片段，保留原始字幕")
                return srt_path

            print(f"成功优化了 {successful_optimizations} 个片段")

            # 合并优化后的片段
            print("合并优化后的字幕片段...")
            merged_subtitles = merge_optimized_segments(original_subtitles, optimized_segments, long_segments)

            # 创建备份
            backup_path = srt_path + '.backup'
            shutil.copy2(srt_path, backup_path)
            print(f"原始字幕已备份到: {backup_path}")

            # 写入优化后的字幕
            if write_srt_file(merged_subtitles, srt_path):
                print(f"字幕质量优化完成，已保存到: {srt_path}")
                print(f"优化统计: 处理了 {len(long_segments)} 个长片段，成功优化 {successful_optimizations} 个")
            else:
                print("写入优化后的字幕失败，恢复原始文件")
                shutil.copy2(backup_path, srt_path)

            return srt_path

        finally:
            # 清理临时目录
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    print(f"临时目录已清理: {temp_dir}")
            except Exception as e:
                print(f"清理临时目录时出错: {e}")

    except Exception as e:
        print(f"字幕质量优化时出错: {e}")
        import traceback
        traceback.print_exc()
        return srt_path


def find_corresponding_video(srt_path):
    """根据字幕文件路径查找对应的视频文件"""
    srt_path = Path(srt_path)
    base_dir = srt_path.parent
    base_name = srt_path.stem

    # 移除可能的后缀，如 "_en", "_optimized" 等
    base_name = base_name.replace('_optimized', '').replace('_en', '').replace('_zh', '')

    # 尝试常见的视频扩展名
    video_extensions = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.ts']

    for ext in video_extensions:
        potential_path = base_dir / (base_name + ext)
        if potential_path.exists():
            print(f"找到对应视频文件: {potential_path}")
            return str(potential_path)

    print(f"无法找到对应的视频文件，搜索基础名称: {base_name}")
    return None
